package com.tocc.domain;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.tocc.common.annotation.Excel;
import com.tocc.common.core.domain.BaseEntity;

import java.io.Serializable;
import java.util.Date;

/**
 * 现场指挥协调会议对象 dispatch_meeting
 * 
 * <AUTHOR>
 * @date 2025-06-09
 */
@Data
public class DispatchMeeting implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 会议id  */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /** 会议主题 */
    @Excel(name = "会议主题")
    private String title;

    /** 主持人 */
    @Excel(name = "主持人")
    private String host;

    /** 会议码 */
    @Excel(name = "会议码")
    private String code;

    /** 频道 */
    @Excel(name = "频道")
    private String channelName;

    /** 邀请链接 */
    @Excel(name = "邀请链接")
    private String inviteUrl;

    /** 会议状态（scheduled:已预约 waiting:等待开始 ongoing:进行中 ended:已结束 cancelled:已取消） */
    @Excel(name = "会议状态", readConverterExp = "scheduled:已预约,waiting:等待开始,ongoing:进行中,ended:已结束,cancelled:已取消")
    private String status;

    /** 描述 */
    @Excel(name = "描述")
    private String description;

    /** 创建者 */
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    /** 创建时间 */
    @TableField(value = "create_time",fill = FieldFill.INSERT)
    private Date createTime;

    /** 更新者 */
    @TableField(value = "update_by",fill = FieldFill.UPDATE)
    private String updateBy;

    /** 更新时间 */
    @TableField(value = "update_time", fill = FieldFill.UPDATE)
    private Date updateTime;

//    public void setId(Long id)
//    {
//        this.id = id;
//    }
//
//    public Long getId()
//    {
//        return id;
//    }
//
//    public void setTitle(String title)
//    {
//        this.title = title;
//    }
//
//    public String getTitle()
//    {
//        return title;
//    }
//
//    public void setHost(String host)
//    {
//        this.host = host;
//    }
//
//    public String getHost()
//    {
//        return host;
//    }
//
//    public void setCode(String code)
//    {
//        this.code = code;
//    }
//
//    public String getCode()
//    {
//        return code;
//    }
//
//    public void setChannelName(String channelName)
//    {
//        this.channelName = channelName;
//    }
//
//    public String getChannelName()
//    {
//        return channelName;
//    }
//
//    public void setInviteUrl(String inviteUrl)
//    {
//        this.inviteUrl = inviteUrl;
//    }
//
//    public String getInviteUrl()
//    {
//        return inviteUrl;
//    }
//
//    public void setStatus(String status)
//    {
//        this.status = status;
//    }
//
//    public String getStatus()
//    {
//        return status;
//    }
//
//    public void setDescription(String description)
//    {
//        this.description = description;
//    }
//
//    public String getDescription()
//    {
//        return description;
//    }
//
//    @Override
//    public String toString() {
//        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
//            .append("id", getId())
//            .append("title", getTitle())
//            .append("host", getHost())
//            .append("code", getCode())
//            .append("channelName", getChannelName())
//            .append("inviteUrl", getInviteUrl())
//            .append("status", getStatus())
//            .append("description", getDescription())
//            .append("createBy", getCreateBy())
//            .append("createTime", getCreateTime())
//            .append("updateBy", getUpdateBy())
//            .append("updateTime", getUpdateTime())
//            .toString();
//    }
}
