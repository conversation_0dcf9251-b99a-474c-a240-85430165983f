<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.tocc</groupId>
        <artifactId>tocc</artifactId>
        <version>3.8.9</version>
    </parent>

    <artifactId>tocc-live</artifactId>
    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <!-- 通用工具-->
        <dependency>
            <groupId>com.tocc</groupId>
            <artifactId>tocc-common</artifactId>
        </dependency>

        <!-- 系统模块-->
        <dependency>
            <groupId>com.tocc</groupId>
            <artifactId>tocc-system</artifactId>
        </dependency>

        <!-- 声网 -->
        <dependency>
            <groupId>io.agora</groupId>
            <artifactId>authentication</artifactId>
            <version>2.1.3</version>
        </dependency>
        <dependency>
            <groupId>com.tocc</groupId>
            <artifactId>tocc-emer</artifactId>
        </dependency>

        <!-- Mybatis-plus 插件 -->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
            <version>3.5.5</version>
        </dependency>
    </dependencies>




</project>