package com.tocc.common.config;

import lombok.Data;

import java.util.Map;

/**
 * API配置项
 * 
 * <AUTHOR>
 */
@Data
public class ApiConfig
{
    private String endpoint;
    private String method = "GET";
    private String description;
    private boolean requireAuth = true;
    private boolean cacheable = false;
    private int cacheTime = 300; // 缓存时间（秒）
    private Map<String, String> defaultHeaders;
    
    public ApiConfig() {}

    public ApiConfig(String endpoint, String method, String description)
    {
        this.endpoint = endpoint;
        this.method = method;
        this.description = description;
    }
} 