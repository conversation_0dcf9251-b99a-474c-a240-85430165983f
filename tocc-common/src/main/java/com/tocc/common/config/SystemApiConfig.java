package com.tocc.common.config;

/**
 * 系统管理接口配置
 * 
 * <AUTHOR>
 */
public class SystemApiConfig 
{
    private String userList = "/system/user/list";
    private String userDetail = "/system/user/{id}";
    private String userCreate = "/system/user";
    private String userUpdate = "/system/user";
    private String userDelete = "/system/user/{id}";
    private String roleList = "/system/role/list";
    private String deptList = "/system/dept/list";
    private String menuList = "/system/menu/list";
    private String dictList = "/system/dict/data/list";

    // getters and setters
    public String getUserList() { return userList; }
    public void setUserList(String userList) { this.userList = userList; }

    public String getUserDetail() { return userDetail; }
    public void setUserDetail(String userDetail) { this.userDetail = userDetail; }

    public String getUserCreate() { return userCreate; }
    public void setUserCreate(String userCreate) { this.userCreate = userCreate; }

    public String getUserUpdate() { return userUpdate; }
    public void setUserUpdate(String userUpdate) { this.userUpdate = userUpdate; }

    public String getUserDelete() { return userDelete; }
    public void setUserDelete(String userDelete) { this.userDelete = userDelete; }

    public String getRoleList() { return roleList; }
    public void setRoleList(String roleList) { this.roleList = roleList; }

    public String getDeptList() { return deptList; }
    public void setDeptList(String deptList) { this.deptList = deptList; }

    public String getMenuList() { return menuList; }
    public void setMenuList(String menuList) { this.menuList = menuList; }

    public String getDictList() { return dictList; }
    public void setDictList(String dictList) { this.dictList = dictList; }
} 