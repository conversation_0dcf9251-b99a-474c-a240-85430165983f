package com.tocc.common.config;

/**
 * 监控数据接口配置
 * 
 * <AUTHOR>
 */
public class MonitorApiConfig
{
    private String systemStatus = "/monitor/system/status";
    private String serverInfo = "/monitor/server/info";
    private String operationLog = "/monitor/operation/log";
    private String loginLog = "/monitor/login/log";
    private String performanceData = "/monitor/performance/data";

    // getters and setters
    public String getSystemStatus() { return systemStatus; }
    public void setSystemStatus(String systemStatus) { this.systemStatus = systemStatus; }

    public String getServerInfo() { return serverInfo; }
    public void setServerInfo(String serverInfo) { this.serverInfo = serverInfo; }

    public String getOperationLog() { return operationLog; }
    public void setOperationLog(String operationLog) { this.operationLog = operationLog; }

    public String getLoginLog() { return loginLog; }
    public void setLoginLog(String loginLog) { this.loginLog = loginLog; }

    public String getPerformanceData() { return performanceData; }
    public void setPerformanceData(String performanceData) { this.performanceData = performanceData; }
} 