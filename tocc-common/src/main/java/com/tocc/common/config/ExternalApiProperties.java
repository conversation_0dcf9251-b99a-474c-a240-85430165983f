package com.tocc.common.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 外部系统API接口配置
 * 
 * <AUTHOR>
 */
@Component
@ConfigurationProperties(prefix = "external.api")
@Data
public class ExternalApiProperties 
{
    /**
     * 系统管理相关接口
     */
    private SystemApiConfig system = new SystemApiConfig();
    
    /**
     * 交通数据相关接口
     */
    private TrafficApiConfig traffic = new TrafficApiConfig();
    
    /**
     * 监控数据相关接口
     */
    private MonitorApiConfig monitor = new MonitorApiConfig();
    
    /**
     * 报表数据相关接口
     */
    private ReportApiConfig report = new ReportApiConfig();
    
    /**
     * 自定义接口配置
     */
    private Map<String, ApiConfig> custom;
} 