package com.tocc.common.config;

import lombok.Data;

/**
 * 交通数据接口配置
 * 
 * <AUTHOR>
 */
@Data
public class TrafficApiConfig
{
    private String stationList = "/traffic/station/list";
    private String stationDetail = "/traffic/station/{id}";
    private String stationPreOneHour = "admin-api/charge/statApi/Api230724/stationPreOneHour";
    private String stationFlowData = "/traffic/station/flow";
    private String roadSectionList = "/traffic/road/list";
    private String roadSectionDetail = "/traffic/road/{id}";
    private String trafficIncident = "/traffic/incident/list";
    private String weatherData = "/traffic/weather/current";
    private String sectionPreOneHour = "charge/roadApi/Api230724/sectionPreOneHour";
    private String getFlowServiceNew = "infra/serviceArea/getFlowServiceNew";
    private String interFlowPreOneHour = "charge/inter/Api230724/interFlowPreOneHour";
    private String stationFlowDetail = "charge/api-v2/stationFlowDetail";
    private String dataHighwayLength = "system/dict-data/page?dictType=data_highway_length&pageSize=100&pageNo=1";
    private String getScreenLeftData = "charge/common/getScreenLeftData";
} 