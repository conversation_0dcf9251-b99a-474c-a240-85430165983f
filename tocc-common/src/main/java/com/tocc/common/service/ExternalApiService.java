package com.tocc.common.service;

import com.alibaba.fastjson2.JSONObject;
import com.tocc.common.config.ApiConfig;
import com.tocc.common.config.ExternalApiProperties;
import com.tocc.common.exception.ServiceException;
import com.tocc.common.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * 外部API服务类
 * 基于配置文件的接口调用服务
 * 
 * <AUTHOR>
 */
@Service
public class ExternalApiService 
{
    private static final Logger log = LoggerFactory.getLogger(ExternalApiService.class);
    
    @Autowired
    private ExternalApiProperties apiProperties;
    
    @Autowired
    private ExternalSystemService externalSystemService;
    
    /**
     * 调用系统管理接口
     * 
     * @param apiName 接口名称（如：userList, userDetail等）
     * @param params 请求参数
     * @param pathVariables 路径变量（用于替换{id}等）
     * @return 接口响应
     */
    public JSONObject callSystemApi(String apiName, Map<String, Object> params, Map<String, String> pathVariables) 
    {
        String endpoint = getSystemApiEndpoint(apiName, pathVariables);
        return externalSystemService.callAuthenticatedGet(endpoint, params);
    }
    
    /**
     * 调用交通数据接口
     * 
     * @param apiName 接口名称（如：stationList, stationPreOneHour等）
     * @param params 请求参数
     * @param pathVariables 路径变量
     * @return 接口响应
     */
    public JSONObject callTrafficApi(String apiName, Map<String, Object> params, Map<String, String> pathVariables) 
    {
        String endpoint = getTrafficApiEndpoint(apiName, pathVariables);
        boolean useCache = shouldUseCache(apiName);
        return externalSystemService.callAuthenticatedGet(endpoint, params, useCache);
    }
    
    /**
     * 调用监控数据接口
     * 
     * @param apiName 接口名称
     * @param params 请求参数
     * @return 接口响应
     */
    public JSONObject callMonitorApi(String apiName, Map<String, Object> params) 
    {
        String endpoint = getMonitorApiEndpoint(apiName);
        return externalSystemService.callAuthenticatedGet(endpoint, params);
    }
    
    /**
     * 调用报表数据接口
     * 
     * @param apiName 接口名称
     * @param params 请求参数
     * @param pathVariables 路径变量
     * @return 接口响应
     */
    public JSONObject callReportApi(String apiName, Map<String, Object> params, Map<String, String> pathVariables) 
    {
        String endpoint = getReportApiEndpoint(apiName, pathVariables);
        return externalSystemService.callAuthenticatedGet(endpoint, params);
    }
    
    /**
     * 调用交通数据接口 - 收费站小时流量
     */
    public JSONObject getStationPreOneHour(Map<String, Object> params) 
    {
        String endpoint = apiProperties.getTraffic().getStationPreOneHour();
        log.info("调用收费站小时流量接口: {}", endpoint);
        return externalSystemService.callAuthenticatedGet(endpoint, params, true);
    }
    
    /**
     * 调用自定义接口
     * 
     * @param apiName 接口名称
     * @param params 请求参数
     * @param data 请求数据（POST等方法使用）
     * @return 接口响应
     */
    public JSONObject callCustomApi(String apiName, Map<String, Object> params, Object data) 
    {
        ApiConfig config = getCustomApiConfig(apiName);
        if (config == null) 
        {
            throw new ServiceException("找不到自定义接口配置: " + apiName);
        }
        
        String endpoint = config.getEndpoint();
        Map<String, String> headers = config.getDefaultHeaders();
        boolean useAuth = config.isRequireAuth();
        boolean useCache = config.isCacheable() && "GET".equalsIgnoreCase(config.getMethod());
        
        log.info("调用自定义接口: {} - {}", apiName, endpoint);
        
        if ("GET".equalsIgnoreCase(config.getMethod())) 
        {
            return externalSystemService.callExternalWithHeaders(endpoint, headers, params, useAuth, useCache);
        } 
        else if ("POST".equalsIgnoreCase(config.getMethod())) 
        {
            return externalSystemService.callExternalPostWithHeaders(endpoint, headers, data, useAuth);
        } 
        else 
        {
            return externalSystemService.callExternalWithHeaders(config.getMethod(), endpoint, headers, params, data, useAuth, useCache);
        }
    }
    
    /**
     * 根据接口名称调用对应的API
     * 
     * @param category 接口分类（system, traffic, monitor, report, custom）
     * @param apiName 接口名称
     * @param params 请求参数
     * @param data 请求数据
     * @param pathVariables 路径变量
     * @return 接口响应
     */
    public JSONObject callApiByName(String category, String apiName, Map<String, Object> params, 
                                   Object data, Map<String, String> pathVariables) 
    {
        switch (category.toLowerCase()) 
        {
            case "traffic":
                return callTrafficApiByName(apiName, params, pathVariables);
            case "custom":
                return callCustomApi(apiName, params, data);
            default:
                throw new ServiceException("不支持的接口分类: " + category);
        }
    }
    
    /**
     * 调用交通接口
     */
    private JSONObject callTrafficApiByName(String apiName, Map<String, Object> params, Map<String, String> pathVariables) 
    {
        String endpoint;
        boolean useCache = false;
        
        switch (apiName.toLowerCase()) 
        {
            case "stationpreonehour":
                endpoint = apiProperties.getTraffic().getStationPreOneHour();
                useCache = true;
                break;
            case "stationlist":
                endpoint = apiProperties.getTraffic().getStationList();
                useCache = true;
                break;
            case "stationflowdata":
                endpoint = apiProperties.getTraffic().getStationFlowData();
                break;
            case "trafficincident":
                endpoint = apiProperties.getTraffic().getTrafficIncident();
                break;
            case "weatherdata":
                endpoint = apiProperties.getTraffic().getWeatherData();
                useCache = true;
                break;
            default:
                throw new ServiceException("未知的交通接口: " + apiName);
        }
        
        endpoint = replacePathVariables(endpoint, pathVariables);
        log.info("调用交通接口: {} - {}", apiName, endpoint);
        
        return externalSystemService.callAuthenticatedGet(endpoint, params, useCache);
    }
    
    /**
     * 获取系统接口端点
     */
    private String getSystemApiEndpoint(String apiName, Map<String, String> pathVariables) 
    {
        String endpoint;
        switch (apiName.toLowerCase()) 
        {
            case "userlist":
                endpoint = apiProperties.getSystem().getUserList();
                break;
            case "userdetail":
                endpoint = apiProperties.getSystem().getUserDetail();
                break;
            case "usercreate":
                endpoint = apiProperties.getSystem().getUserCreate();
                break;
            case "userupdate":
                endpoint = apiProperties.getSystem().getUserUpdate();
                break;
            case "userdelete":
                endpoint = apiProperties.getSystem().getUserDelete();
                break;
            case "rolelist":
                endpoint = apiProperties.getSystem().getRoleList();
                break;
            case "deptlist":
                endpoint = apiProperties.getSystem().getDeptList();
                break;
            case "menulist":
                endpoint = apiProperties.getSystem().getMenuList();
                break;
            case "dictlist":
                endpoint = apiProperties.getSystem().getDictList();
                break;
            default:
                throw new ServiceException("未知的系统接口: " + apiName);
        }
        
        return replacePathVariables(endpoint, pathVariables);
    }
    
    /**
     * 获取交通接口端点
     */
    private String getTrafficApiEndpoint(String apiName, Map<String, String> pathVariables) 
    {
        String endpoint;
        switch (apiName.toLowerCase()) 
        {
            case "stationlist":
                endpoint = apiProperties.getTraffic().getStationList();
                break;
            case "stationdetail":
                endpoint = apiProperties.getTraffic().getStationDetail();
                break;
            case "stationpreonehour":
                endpoint = apiProperties.getTraffic().getStationPreOneHour();
                break;
            case "stationflowdata":
                endpoint = apiProperties.getTraffic().getStationFlowData();
                break;
            case "roadsectionlist":
                endpoint = apiProperties.getTraffic().getRoadSectionList();
                break;
            case "roadsectiondetail":
                endpoint = apiProperties.getTraffic().getRoadSectionDetail();
                break;
            case "trafficincident":
                endpoint = apiProperties.getTraffic().getTrafficIncident();
                break;
            case "weatherdata":
                endpoint = apiProperties.getTraffic().getWeatherData();
                break;
            default:
                throw new ServiceException("未知的交通接口: " + apiName);
        }
        
        return replacePathVariables(endpoint, pathVariables);
    }
    
    /**
     * 获取监控接口端点
     */
    private String getMonitorApiEndpoint(String apiName) 
    {
        switch (apiName.toLowerCase()) 
        {
            case "systemstatus":
                return apiProperties.getMonitor().getSystemStatus();
            case "serverinfo":
                return apiProperties.getMonitor().getServerInfo();
            case "operationlog":
                return apiProperties.getMonitor().getOperationLog();
            case "loginlog":
                return apiProperties.getMonitor().getLoginLog();
            case "performancedata":
                return apiProperties.getMonitor().getPerformanceData();
            default:
                throw new ServiceException("未知的监控接口: " + apiName);
        }
    }
    
    /**
     * 获取报表接口端点
     */
    private String getReportApiEndpoint(String apiName, Map<String, String> pathVariables) 
    {
        String endpoint;
        switch (apiName.toLowerCase()) 
        {
            case "trafficreport":
                endpoint = apiProperties.getReport().getTrafficReport();
                break;
            case "statisticsreport":
                endpoint = apiProperties.getReport().getStatisticsReport();
                break;
            case "exportdata":
                endpoint = apiProperties.getReport().getExportData();
                break;
            case "chartdata":
                endpoint = apiProperties.getReport().getChartData();
                break;
            default:
                throw new ServiceException("未知的报表接口: " + apiName);
        }
        
        return replacePathVariables(endpoint, pathVariables);
    }
    
    /**
     * 获取自定义接口配置
     */
    private ApiConfig getCustomApiConfig(String apiName) 
    {
        if (apiProperties.getCustom() == null) 
        {
            return null;
        }
        return apiProperties.getCustom().get(apiName);
    }
    
    /**
     * 替换路径变量
     */
    private String replacePathVariables(String endpoint, Map<String, String> pathVariables) 
    {
        if (pathVariables == null || pathVariables.isEmpty()) 
        {
            return endpoint;
        }
        
        String result = endpoint;
        for (Map.Entry<String, String> entry : pathVariables.entrySet()) 
        {
            result = result.replace("{" + entry.getKey() + "}", entry.getValue());
        }
        
        return result;
    }
    
    /**
     * 判断是否应该使用缓存
     */
    private boolean shouldUseCache(String apiName) 
    {
        // 对于某些实时性要求不高的接口可以使用缓存
        switch (apiName.toLowerCase()) 
        {
            case "stationlist":
            case "roadsectionlist":
            case "weatherdata":
                return true;
            default:
                return false;
        }
    }
    
    /**
     * 获取所有可用的API配置信息
     */
    public Map<String, Object> getApiConfigInfo() 
    {
        Map<String, Object> configInfo = new HashMap<>();
        
        // 系统接口
        Map<String, String> systemApis = new HashMap<>();
        systemApis.put("userList", apiProperties.getSystem().getUserList());
        systemApis.put("userDetail", apiProperties.getSystem().getUserDetail());
        systemApis.put("roleList", apiProperties.getSystem().getRoleList());
        systemApis.put("deptList", apiProperties.getSystem().getDeptList());
        configInfo.put("system", systemApis);
        
        // 交通接口
        Map<String, String> trafficApis = new HashMap<>();
        trafficApis.put("stationList", apiProperties.getTraffic().getStationList());
        trafficApis.put("stationPreOneHour", apiProperties.getTraffic().getStationPreOneHour());
        trafficApis.put("stationFlowData", apiProperties.getTraffic().getStationFlowData());
        trafficApis.put("trafficIncident", apiProperties.getTraffic().getTrafficIncident());
        trafficApis.put("weatherData", apiProperties.getTraffic().getWeatherData());
        configInfo.put("traffic", trafficApis);
        
        // 监控接口
        Map<String, String> monitorApis = new HashMap<>();
        monitorApis.put("systemStatus", apiProperties.getMonitor().getSystemStatus());
        monitorApis.put("serverInfo", apiProperties.getMonitor().getServerInfo());
        monitorApis.put("performanceData", apiProperties.getMonitor().getPerformanceData());
        configInfo.put("monitor", monitorApis);
        
        // 报表接口
        Map<String, String> reportApis = new HashMap<>();
        reportApis.put("trafficReport", apiProperties.getReport().getTrafficReport());
        reportApis.put("statisticsReport", apiProperties.getReport().getStatisticsReport());
        reportApis.put("chartData", apiProperties.getReport().getChartData());
        configInfo.put("report", reportApis);
        
        // 自定义接口
        if (apiProperties.getCustom() != null) 
        {
            Map<String, Object> customApis = new HashMap<>();
            for (Map.Entry<String, ApiConfig> entry : apiProperties.getCustom().entrySet()) 
            {
                ApiConfig config = entry.getValue();
                Map<String, Object> apiInfo = new HashMap<>();
                apiInfo.put("endpoint", config.getEndpoint());
                apiInfo.put("method", config.getMethod());
                apiInfo.put("description", config.getDescription());
                apiInfo.put("requireAuth", config.isRequireAuth());
                apiInfo.put("cacheable", config.isCacheable());
                apiInfo.put("cacheTime", config.getCacheTime());
                customApis.put(entry.getKey(), apiInfo);
            }
            configInfo.put("custom", customApis);
        }
        
        return configInfo;
    }
} 