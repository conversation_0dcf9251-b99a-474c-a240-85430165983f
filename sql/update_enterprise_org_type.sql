-- 更新企业组织类型脚本
-- 将现有的企业数据的 org_type 从 '1' 更新为 '2'

-- 备份当前数据（可选）
-- CREATE TABLE sys_dept_backup_20250618 AS SELECT * FROM sys_dept WHERE org_type = '1';

-- 更新企业的组织类型
-- 注意：这里需要根据实际情况识别哪些是企业
-- 方法1：根据部门名称包含"企业"关键字来识别
UPDATE sys_dept 
SET org_type = '2' 
WHERE org_type = '1' 
  AND (dept_name LIKE '%企业%' OR dept_name LIKE '%公司%' OR dept_name LIKE '%集团%');

-- 方法2：根据特定的父级ID来识别企业（如果企业都在特定的父级下）
-- UPDATE sys_dept 
-- SET org_type = '2' 
-- WHERE org_type = '1' 
--   AND parent_id = 某个特定的父级ID;

-- 方法3：根据具体的部门ID列表来更新（最精确的方法）
-- UPDATE sys_dept 
-- SET org_type = '2' 
-- WHERE dept_id IN (具体的企业部门ID列表);

-- 验证更新结果
SELECT dept_id, dept_name, org_type, parent_id 
FROM sys_dept 
WHERE org_type = '2' 
ORDER BY dept_name;

-- 检查是否还有需要更新的企业
SELECT dept_id, dept_name, org_type, parent_id 
FROM sys_dept 
WHERE org_type = '1' 
  AND (dept_name LIKE '%企业%' OR dept_name LIKE '%公司%' OR dept_name LIKE '%集团%')
ORDER BY dept_name;
