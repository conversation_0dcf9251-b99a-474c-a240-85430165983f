-- 测试数据权限控制脚本
-- 用于验证企业数据权限是否正常工作

-- 1. 查看当前组织架构
SELECT 
    dept_id,
    dept_name,
    org_type,
    CASE org_type 
        WHEN '0' THEN '部门'
        WHEN '1' THEN '单位'
        WHEN '2' THEN '企业'
        ELSE '未知'
    END as org_type_name,
    parent_id,
    ancestors
FROM sys_dept 
WHERE del_flag = '0'
ORDER BY org_type, order_num, dept_name;

-- 2. 查看企业数据
SELECT 
    dept_id,
    dept_name,
    org_type,
    parent_id,
    ancestors
FROM sys_dept 
WHERE org_type = '2' AND del_flag = '0'
ORDER BY dept_name;

-- 3. 查看用户的组织归属
SELECT 
    u.user_id,
    u.user_name,
    u.nick_name,
    u.dept_id,
    u.org_id,
    d.dept_name,
    d.org_type,
    CASE d.org_type 
        WHEN '0' THEN '部门'
        WHEN '1' THEN '单位'
        WHEN '2' THEN '企业'
        ELSE '未知'
    END as org_type_name
FROM sys_user u
LEFT JOIN sys_dept d ON u.dept_id = d.dept_id
WHERE u.del_flag = '0'
ORDER BY u.user_name;

-- 4. 查看角色的数据权限设置
SELECT 
    r.role_id,
    r.role_name,
    r.data_scope,
    CASE r.data_scope 
        WHEN '1' THEN '所有数据权限'
        WHEN '2' THEN '自定义数据权限'
        WHEN '3' THEN '本单位数据权限+企业数据权限'
        WHEN '4' THEN '本单位及以下数据权限+企业数据权限'
        WHEN '5' THEN '仅本人数据权限'
        ELSE '未知'
    END as data_scope_name
FROM sys_role r
WHERE r.del_flag = '0'
ORDER BY r.role_sort;

-- 5. 模拟数据权限SQL生成测试
-- 假设用户org_id = 100，测试生成的SQL条件

-- 本单位数据权限+企业数据权限 (DATA_SCOPE_DEPT = "3")
-- 生成的SQL应该类似：
-- AND (d.dept_id = 100 OR d.dept_id IN (SELECT dept_id FROM sys_dept WHERE org_type = '2'))

-- 本单位及以下数据权限+企业数据权限 (DATA_SCOPE_DEPT_AND_CHILD = "4")  
-- 生成的SQL应该类似：
-- AND (d.dept_id IN ( SELECT dept_id FROM sys_dept WHERE org_type = '1' AND (dept_id = 100 or find_in_set( 100 , ancestors )) ) OR d.dept_id IN (SELECT dept_id FROM sys_dept WHERE org_type = '2'))

-- 6. 验证企业数据查询
-- 测试查询所有企业数据
SELECT dept_id FROM sys_dept WHERE org_type = '2';

-- 7. 验证单位及下级查询
-- 假设用户org_id = 100，测试查询本单位及下级
SELECT dept_id FROM sys_dept WHERE org_type = '1' AND (dept_id = 100 or find_in_set(100, ancestors));
