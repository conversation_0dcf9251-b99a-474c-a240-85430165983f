-- ----------------------------
-- 应急事件关联企业表
-- ----------------------------
DROP TABLE IF EXISTS `emergency_event_enterprise`;
CREATE TABLE `emergency_event_enterprise` (
  `id` varchar(64) NOT NULL COMMENT '主键ID',
  `event_id` varchar(64) NOT NULL COMMENT '应急事件ID',
  `enterprise_personnel_id` varchar(64) NOT NULL COMMENT '企业人员信息ID',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `creator` varchar(50) DEFAULT NULL COMMENT '创建人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `updater` varchar(50) DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_event_enterprise` (`event_id`, `enterprise_personnel_id`),
  KEY `idx_event_id` (`event_id`),
  KEY `idx_enterprise_id` (`enterprise_personnel_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='应急事件关联企业表';
