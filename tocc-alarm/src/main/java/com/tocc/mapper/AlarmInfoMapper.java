package com.tocc.mapper;

import com.tocc.domain.dto.AlarmInfoDTO;
import com.tocc.domain.vo.AlarmInfoVO;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 告警信息Mapper接口
 * 
 * <AUTHOR>
 */
public interface AlarmInfoMapper {
    
    /**
     * 查询告警信息
     * 
     * @param alarmId 告警信息主键
     * @return 告警信息
     */
    AlarmInfoVO selectAlarmInfoByAlarmId(String alarmId);

    /**
     * 查询告警信息列表
     * 
     * @param alarmInfo 告警信息
     * @return 告警信息集合
     */
    List<AlarmInfoVO> selectAlarmInfoList(AlarmInfoDTO alarmInfo);

    /**
     * 新增告警信息
     * 
     * @param alarmInfo 告警信息
     * @return 结果
     */
    int insertAlarmInfo(AlarmInfoDTO alarmInfo);

    /**
     * 修改告警信息
     * 
     * @param alarmInfo 告警信息
     * @return 结果
     */
    int updateAlarmInfo(AlarmInfoDTO alarmInfo);

    /**
     * 删除告警信息
     * 
     * @param alarmId 告警信息主键
     * @return 结果
     */
    int deleteAlarmInfoByAlarmId(String alarmId);

    /**
     * 批量删除告警信息
     * 
     * @param alarmIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteAlarmInfoByAlarmIds(String[] alarmIds);

    /**
     * 更新告警状态
     * 
     * @param alarmId 告警ID
     * @param status 新状态
     * @param processorId 处理人ID
     * @param processorName 处理人姓名
     * @param processResult 处理结果
     * @param updateBy 更新者
     * @return 结果
     */
    int updateAlarmStatus(@Param("alarmId") String alarmId, 
                         @Param("status") String status,
                         @Param("processorId") String processorId,
                         @Param("processorName") String processorName,
                         @Param("processResult") String processResult,
                         @Param("updateBy") String updateBy);

    /**
     * 统计告警数量
     * 
     * @param alarmInfo 查询条件
     * @return 统计数量
     */
    int countAlarmInfo(AlarmInfoDTO alarmInfo);



    /**
     * 检查是否存在相同的超时告警
     *
     * @param infoType 信息类型
     * @param infoId 信息ID
     * @param timeLimit 时间限制
     * @return 是否存在
     */
    boolean existsTimeoutAlarm(@Param("infoType") String infoType, @Param("infoId") String infoId, @Param("timeLimit") LocalDateTime timeLimit);

    /**
     * 查询气象预警基础信息
     *
     * @param warningId 预警ID
     * @return 预警信息
     */
    Map<String, Object> selectWeatherWarningByWarningId(@Param("warningId") String warningId);

    /**
     * 查询气象预警信息（包含统计）
     *
     * @param warningId 预警ID
     * @return 预警信息（包含统计数据）
     */
    Map<String, Object> selectWeatherWarningWithStats(@Param("warningId") String warningId);

    /**
     * 查询气象预警影响区域
     *
     * @param warningId 预警ID
     * @return 影响区域列表
     */
    List<Map<String, Object>> selectWeatherWarningAreas(@Param("warningId") String warningId);

    /**
     * 查询气象预警通知记录
     *
     * @param warningId 预警ID
     * @param contactUserId 联系人用户ID
     * @return 通知记录
     */
    Map<String, Object> selectWeatherWarningNotificationByIds(@Param("warningId") String warningId, @Param("contactUserId") Long contactUserId);

    /**
     * 查询单位内所有通知记录
     *
     * @param warningId 预警ID
     * @param orgId 单位ID
     * @return 通知记录列表
     */
    List<Map<String, Object>> selectNotificationsByWarningIdAndOrgId(@Param("warningId") String warningId, @Param("orgId") String orgId);

    /**
     * 查询单位及上层单位创建的预警列表
     *
     * @param orgId 单位ID
     * @return 预警列表
     */
    List<Map<String, Object>> selectWarningsByOrgId(@Param("orgId") String orgId);

    /**
     * 查询预警通知进展
     *
     * @param warningId 预警ID
     * @return 通知进展列表
     */
    List<Map<String, Object>> selectNotificationProgress(@Param("warningId") String warningId);
}
