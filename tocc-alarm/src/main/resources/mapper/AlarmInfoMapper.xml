<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tocc.mapper.AlarmInfoMapper">
    
    <resultMap type="com.tocc.domain.vo.AlarmInfoVO" id="AlarmInfoResult">
        <result property="alarmId"       column="alarm_id"       />
        <result property="alarmTitle"    column="alarm_title"    />
        <result property="alarmType"     column="alarm_type"     />
        <result property="alarmTypeName" column="alarm_type_name" />
        <result property="alarmSubtype"  column="alarm_subtype"  />
        <result property="alarmSubtypeName" column="alarm_subtype_name" />
        <result property="alarmLevel"    column="alarm_level"    />
        <result property="alarmLevelName" column="alarm_level_name" />
        <result property="alarmContent"  column="alarm_content"  />
        <result property="sourceId"      column="source_id"      />
        <result property="sourceType"    column="source_type"    />
        <result property="sourceTypeName" column="source_type_name" />
        <result property="orgId"         column="org_id"         />
        <result property="orgName"       column="org_name"       />
        <result property="status"        column="status"         />
        <result property="statusName"    column="status_name"    />
        <result property="alarmTime"     column="alarm_time"     />
        <result property="processTime"   column="process_time"   />
        <result property="processorId"   column="processor_id"   />
        <result property="processorName" column="processor_name" />
        <result property="processResult" column="process_result" />
        <result property="createBy"      column="create_by"      />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"      column="update_by"      />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"        column="remark"         />
        <result property="administrativeAreaId" column="administrative_area_id" />
        <result property="administrativeArea" column="administrative_area" />
    </resultMap>

    <sql id="selectAlarmInfoVo">
        select 
            a.alarm_id, a.alarm_title, a.alarm_type, a.alarm_subtype, a.alarm_level,
            a.alarm_content, a.source_id, a.source_type, a.org_id, a.org_name,
            a.status, a.alarm_time, a.process_time, a.processor_id, a.processor_name,
            a.process_result, a.create_by, a.create_time, a.update_by, a.update_time, a.remark,
            a.administrative_area_id, a.administrative_area,
            -- 字典值转换
            dt1.dict_label as alarm_type_name,
            dt2.dict_label as alarm_subtype_name,
            dt3.dict_label as alarm_level_name,
            dt4.dict_label as source_type_name,
            case a.status 
                when '0' then '未处理'
                when '1' then '已处理'
                when '2' then '已忽略'
                else '未知'
            end as status_name
        from alarm_info a
        left join sys_dict_data dt1 on dt1.dict_value = a.alarm_type and dt1.dict_type = 'alarm_type'
        left join sys_dict_data dt2 on dt2.dict_value = a.alarm_subtype and dt2.dict_type = 'alarm_subtype'
        left join sys_dict_data dt3 on dt3.dict_value = a.alarm_level and dt3.dict_type = 'alarm_level'
        left join sys_dict_data dt4 on dt4.dict_value = a.source_type and dt4.dict_type = 'source_type'
    </sql>

    <select id="selectAlarmInfoList" parameterType="com.tocc.domain.dto.AlarmInfoDTO" resultMap="AlarmInfoResult">
        <include refid="selectAlarmInfoVo"/>
        <where>
            <if test="alarmTitle != null and alarmTitle != ''"> and a.alarm_title like concat('%', #{alarmTitle}, '%')</if>
            <if test="alarmType != null and alarmType != ''"> and a.alarm_type = #{alarmType}</if>
            <if test="alarmSubtype != null and alarmSubtype != ''"> and a.alarm_subtype = #{alarmSubtype}</if>
            <if test="alarmLevel != null and alarmLevel != ''"> and a.alarm_level = #{alarmLevel}</if>
            <if test="sourceType != null and sourceType != ''"> and a.source_type = #{sourceType}</if>
            <if test="orgId != null and orgId != ''"> and a.org_id = #{orgId}</if>
            <if test="status != null and status != ''"> and a.status = #{status}</if>
            <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
                and date_format(a.alarm_time,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
            </if>
            <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
                and date_format(a.alarm_time,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
            </if>
            <!-- 数据范围过滤 -->
            ${params.dataScope}
        </where>
        order by a.alarm_time desc
    </select>
    
    <select id="selectAlarmInfoByAlarmId" parameterType="String" resultMap="AlarmInfoResult">
        <include refid="selectAlarmInfoVo"/>
        where a.alarm_id = #{alarmId}
    </select>



    <select id="countAlarmInfo" parameterType="com.tocc.domain.dto.AlarmInfoDTO" resultType="int">
        select count(*) from alarm_info a
        <where>  
            <if test="alarmType != null and alarmType != ''"> and a.alarm_type = #{alarmType}</if>
            <if test="alarmSubtype != null and alarmSubtype != ''"> and a.alarm_subtype = #{alarmSubtype}</if>
            <if test="alarmLevel != null and alarmLevel != ''"> and a.alarm_level = #{alarmLevel}</if>
            <if test="sourceType != null and sourceType != ''"> and a.source_type = #{sourceType}</if>
            <if test="orgId != null and orgId != ''"> and a.org_id = #{orgId}</if>
            <if test="status != null and status != ''"> and a.status = #{status}</if>
        </where>
    </select>
        
    <insert id="insertAlarmInfo" parameterType="com.tocc.domain.dto.AlarmInfoDTO">
        insert into alarm_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="alarmId != null">alarm_id,</if>
            <if test="alarmTitle != null">alarm_title,</if>
            <if test="alarmType != null">alarm_type,</if>
            <if test="alarmSubtype != null">alarm_subtype,</if>
            <if test="alarmLevel != null">alarm_level,</if>
            <if test="alarmContent != null">alarm_content,</if>
            <if test="sourceId != null">source_id,</if>
            <if test="sourceType != null">source_type,</if>
            <if test="orgId != null">org_id,</if>
            <if test="orgName != null">org_name,</if>
            <if test="status != null">status,</if>
            <if test="alarmTime != null">alarm_time,</if>
            <if test="processTime != null">process_time,</if>
            <if test="processorId != null">processor_id,</if>
            <if test="processorName != null">processor_name,</if>
            <if test="processResult != null">process_result,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
            <if test="administrativeArea != null">administrative_area,</if>
            <if test="administrativeAreaId != null">administrative_area_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="alarmId != null">#{alarmId},</if>
            <if test="alarmTitle != null">#{alarmTitle},</if>
            <if test="alarmType != null">#{alarmType},</if>
            <if test="alarmSubtype != null">#{alarmSubtype},</if>
            <if test="alarmLevel != null">#{alarmLevel},</if>
            <if test="alarmContent != null">#{alarmContent},</if>
            <if test="sourceId != null">#{sourceId},</if>
            <if test="sourceType != null">#{sourceType},</if>
            <if test="orgId != null">#{orgId},</if>
            <if test="orgName != null">#{orgName},</if>
            <if test="status != null">#{status},</if>
            <if test="alarmTime != null">#{alarmTime},</if>
            <if test="processTime != null">#{processTime},</if>
            <if test="processorId != null">#{processorId},</if>
            <if test="processorName != null">#{processorName},</if>
            <if test="processResult != null">#{processResult},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="administrativeArea != null">#{administrativeArea},</if>
            <if test="administrativeAreaId != null">#{administrativeAreaId},</if>
         </trim>
    </insert>

    <update id="updateAlarmInfo" parameterType="com.tocc.domain.dto.AlarmInfoDTO">
        update alarm_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="alarmTitle != null">alarm_title = #{alarmTitle},</if>
            <if test="alarmType != null">alarm_type = #{alarmType},</if>
            <if test="alarmSubtype != null">alarm_subtype = #{alarmSubtype},</if>
            <if test="alarmLevel != null">alarm_level = #{alarmLevel},</if>
            <if test="alarmContent != null">alarm_content = #{alarmContent},</if>
            <if test="sourceId != null">source_id = #{sourceId},</if>
            <if test="sourceType != null">source_type = #{sourceType},</if>
            <if test="orgId != null">org_id = #{orgId},</if>
            <if test="orgName != null">org_name = #{orgName},</if>
            <if test="status != null">status = #{status},</if>
            <if test="alarmTime != null">alarm_time = #{alarmTime},</if>
            <if test="processTime != null">process_time = #{processTime},</if>
            <if test="processorId != null">processor_id = #{processorId},</if>
            <if test="processorName != null">processor_name = #{processorName},</if>
            <if test="processResult != null">process_result = #{processResult},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="administrativeArea != null">administrative_area = #{administrativeArea},</if>
            <if test="administrativeAreaId != null">administrative_area_id = #{administrativeAreaId},</if>
        </trim>
        where alarm_id = #{alarmId}
    </update>

    <update id="updateAlarmStatus">
        update alarm_info 
        set status = #{status},
            process_time = now(),
            processor_id = #{processorId},
            processor_name = #{processorName},
            process_result = #{processResult},
            update_by = #{updateBy},
            update_time = now()
        where alarm_id = #{alarmId}
    </update>

    <delete id="deleteAlarmInfoByAlarmId" parameterType="String">
        delete from alarm_info where alarm_id = #{alarmId}
    </delete>

    <delete id="deleteAlarmInfoByAlarmIds" parameterType="String">
        delete from alarm_info where alarm_id in
        <foreach item="alarmId" collection="array" open="(" separator="," close=")">
            #{alarmId}
        </foreach>
    </delete>

    <select id="existsTimeoutAlarm" resultType="boolean">
        select count(1) > 0
        from alarm_info
        where alarm_type = '2'
          and source_type = #{infoType}
          and source_id = #{infoId}
          and alarm_time >= #{timeLimit}
    </select>

    <!-- 气象预警相关查询 -->
    <select id="selectWeatherWarningByWarningId" parameterType="String" resultType="java.util.Map">
        select warning_id, warning_type, warning_level, warning_content, prevention_guide,
               affected_roads, issue_time, expire_time, status, is_notified, create_by, create_time,
               update_by, update_time, remark
        from weather_warning
        where warning_id = #{warningId}
    </select>

    <select id="selectWeatherWarningWithStats" parameterType="String" resultType="java.util.Map">
        select w.warning_id, w.warning_type, w.warning_level, w.warning_content, w.prevention_guide,
               w.affected_roads, w.issue_time, w.expire_time, w.status, w.is_notified, w.create_by, w.create_time,
               w.update_by, w.update_time, w.remark,
               COALESCE(n.total_notifications, 0) as total_notifications,
               COALESCE(n.confirmed_notifications, 0) as confirmed_notifications,
               COALESCE(n.unconfirmed_notifications, 0) as unconfirmed_notifications,
               COALESCE(n.timeout_notifications, 0) as timeout_notifications
        from weather_warning w
        left join (
            select warning_id,
                   count(*) as total_notifications,
                   sum(case when confirm_status = '1' then 1 else 0 end) as confirmed_notifications,
                   sum(case when confirm_status = '0' then 1 else 0 end) as unconfirmed_notifications,
                   sum(case when is_timeout = '1' then 1 else 0 end) as timeout_notifications
            from weather_warning_notification
            group by warning_id
        ) n on w.warning_id = n.warning_id
        where w.warning_id = #{warningId}
    </select>

    <select id="selectWeatherWarningAreas" parameterType="String" resultType="java.util.Map">
        select warning_id, region_id, region_name, create_time
        from weather_warning_area
        where warning_id = #{warningId}
        order by create_time
    </select>

    <select id="selectWeatherWarningNotificationByIds" resultType="java.util.Map">
        select warning_id, contact_user_id, contact_unit_name, contact_dept_name, contact_post_name,
               contact_user_name, contact_phone, contact_email, notification_time, confirm_status,
               confirm_time, confirm_user_id, confirm_user_name, timeout_minutes, is_timeout,
               alarm_id, create_time, update_time
        from weather_warning_notification
        where warning_id = #{warningId} and contact_user_id = #{contactUserId}
    </select>

    <select id="selectNotificationsByWarningIdAndOrgId" resultType="java.util.Map">
        select warning_id, contact_user_id, contact_unit_name, contact_dept_name, contact_post_name,
               contact_user_name, contact_phone, contact_email, notification_time, confirm_status,
               confirm_time, confirm_user_id, confirm_user_name, timeout_minutes, is_timeout,
               alarm_id, create_time, update_time
        from weather_warning_notification
        where warning_id = #{warningId}
        and exists (
            select 1 from sys_user u
            where u.user_id = contact_user_id
            and u.org_id = #{orgId}
        )
    </select>

    <select id="selectWarningsByOrgId" parameterType="String" resultType="java.util.Map">
        select warning_id, warning_type, warning_level, warning_content, prevention_guide,
               affected_roads, issue_time, expire_time, status, is_notified, create_by, create_time,
               update_by, update_time, remark
        from weather_warning w
        where exists (
            select 1 from sys_user u
            where u.user_name = w.create_by
            and u.org_id = #{orgId}
        )
    </select>

    <select id="selectNotificationProgress" parameterType="String" resultType="java.util.Map">
        select contact_user_name, contact_unit_name, contact_dept_name, contact_post_name,
               contact_phone, notification_time, confirm_status, confirm_time,
               confirm_user_name, is_timeout, timeout_minutes
        from weather_warning_notification
        where warning_id = #{warningId}
        order by notification_time desc
    </select>
</mapper>
