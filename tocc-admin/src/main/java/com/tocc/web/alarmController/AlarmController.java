package com.tocc.web.alarmController;

import com.tocc.common.annotation.Anonymous;
import com.tocc.common.annotation.Log;
import com.tocc.common.core.controller.BaseController;
import com.tocc.common.core.domain.AjaxResult;
import com.tocc.common.core.page.TableDataInfo;
import com.tocc.common.enums.BusinessType;
import com.tocc.common.utils.poi.ExcelUtil;
import com.tocc.common.utils.StringUtils;
import com.tocc.domain.dto.AlarmInfoDTO;
import com.tocc.domain.vo.AlarmInfoVO;
import com.tocc.service.IAlarmService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 告警信息Controller
 * 
 * <AUTHOR>
 */
@Api(tags = "告警信息管理")
@RestController
@RequestMapping("/alarm/info")
public class AlarmController extends BaseController {

    @Autowired
    private IAlarmService alarmService;

    /**
     * 查询告警信息列表
     */
    @ApiOperation("查询告警信息列表")
    @PreAuthorize("@ss.hasPermi('alarm:info:list')")
    @GetMapping("/list")
    public TableDataInfo list(AlarmInfoDTO alarmInfo,
                             @RequestParam(value = "alarm_subtype", required = false) String alarmSubtypeStr) {

        // 处理逗号分隔的子类型参数
        if (StringUtils.isNotEmpty(alarmSubtypeStr)) {
            String[] subtypes = alarmSubtypeStr.split(",");
            alarmInfo.setAlarmSubtypes(Arrays.asList(subtypes));
        }

        // 构建查询条件
        startPage();
        List<AlarmInfoVO> list = alarmService.selectAlarmInfoList(alarmInfo);
        return getDataTable(list);
    }

    /**
     * 导出告警信息列表
     */
    @ApiOperation("导出告警信息列表")
    @PreAuthorize("@ss.hasPermi('alarm:info:export')")
    @Log(title = "告警信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AlarmInfoDTO alarmInfo) {
        List<AlarmInfoVO> list = alarmService.selectAlarmInfoList(alarmInfo);
        ExcelUtil<AlarmInfoVO> util = new ExcelUtil<AlarmInfoVO>(AlarmInfoVO.class);
        util.exportExcel(response, list, "告警信息数据");
    }

    /**
     * 获取告警信息详细信息
     */
    @ApiOperation("获取告警信息详细信息")
    @PreAuthorize("@ss.hasPermi('alarm:info:query')")
    @GetMapping(value = "/{alarmId}")
    public AjaxResult getInfo(@ApiParam(value = "告警ID", required = true) @PathVariable("alarmId") String alarmId) {
        return success(alarmService.selectAlarmInfoByAlarmId(alarmId));
    }

    /**
     * 新增告警信息
     */
    @ApiOperation("新增告警信息")
    @Anonymous
    @PostMapping("/add")
    public AjaxResult add(@Validated @RequestBody AlarmInfoDTO alarmInfo) {
        return toAjax(alarmService.insertAlarmInfo(alarmInfo));
    }

    /**
     * 修改告警信息
     */
    @ApiOperation("修改告警信息")
    @Anonymous
    @PutMapping("/edit")
    public AjaxResult edit(@Validated @RequestBody AlarmInfoDTO alarmInfo) {
        return toAjax(alarmService.updateAlarmInfo(alarmInfo));
    }

    /**
     * 删除告警信息
     */
    @ApiOperation("删除告警信息")
    @PreAuthorize("@ss.hasPermi('alarm:info:remove')")
    @Log(title = "告警信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{alarmIds}")
    public AjaxResult remove(@ApiParam(value = "告警ID数组", required = true) @PathVariable String[] alarmIds) {
        return toAjax(alarmService.deleteAlarmInfoByAlarmIds(alarmIds));
    }

    /**
     * 处理告警
     */
    @ApiOperation("处理告警")
    @PreAuthorize("@ss.hasPermi('alarm:info:process')")
    @Log(title = "告警处理", businessType = BusinessType.UPDATE)
    @PutMapping("/process")
    public AjaxResult processAlarm(@RequestParam String alarmId, 
                                  @RequestParam String status, 
                                  @RequestParam(required = false) String processResult) {
        return toAjax(alarmService.processAlarm(alarmId, status, processResult));
    }

    /**
     * 统计告警数量
     */
    @ApiOperation("统计告警数量")
    @PreAuthorize("@ss.hasPermi('alarm:info:list')")
    @GetMapping("/count")
    public AjaxResult count(AlarmInfoDTO alarmInfo) {
        int count = alarmService.countAlarmInfo(alarmInfo);
        return success(count);
    }

    /**
     * 获取未处理告警数量
     */
    @ApiOperation("获取未处理告警数量")
    @PreAuthorize("@ss.hasPermi('alarm:info:list')")
    @GetMapping("/unprocessedCount")
    public AjaxResult getUnprocessedCount() {
        AlarmInfoDTO alarmInfo = new AlarmInfoDTO();
        alarmInfo.setStatus("0"); // 未处理
        int count = alarmService.countAlarmInfo(alarmInfo);
        return success(count);
    }

    /**
     * 根据告警类型统计数量
     */
    @ApiOperation("根据告警类型统计数量")
    @PreAuthorize("@ss.hasPermi('alarm:info:list')")
    @GetMapping("/countByType")
    public AjaxResult countByType(@RequestParam String alarmType) {
        AlarmInfoDTO alarmInfo = new AlarmInfoDTO();
        alarmInfo.setAlarmType(alarmType);
        int count = alarmService.countAlarmInfo(alarmInfo);
        return success(count);
    }

    /**
     * 获取告警数据源详情
     */
    @ApiOperation("获取告警数据源详情")
    @PreAuthorize("@ss.hasPermi('alarm:info:query')")
    @GetMapping("/source/{alarmId}")
    public AjaxResult getAlarmSourceDetail(@ApiParam(value = "告警ID", required = true) @PathVariable("alarmId") String alarmId) {
        Map<String, Object> sourceDetail = alarmService.getAlarmSourceDetail(alarmId);
        if (sourceDetail == null) {
            return error("告警源数据不存在");
        }
        return success(sourceDetail);
    }
}
