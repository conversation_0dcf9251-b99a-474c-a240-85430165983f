package com.tocc.web.drillPlanController;


import com.tocc.common.core.controller.BaseController;
import com.tocc.common.core.domain.AjaxResult;
import com.tocc.common.core.page.TableDataInfo;
import com.tocc.domain.dto.*;
import com.tocc.service.*;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/drill")
@RequiredArgsConstructor
public class DrillPlanController extends BaseController {

    private final IDrillPlanService drillPlanService;

    private final IDrillDataService drillDataService;

    private final IDrillReviewReportService drillReviewReportService;

    private final IDrillReminderService drillReminderService;

    private final IDrillDesktopService drillDesktopService;

    private final IDrillLogService drillLogService;

    private final IDrillDesktopTaskService drillDesktopTaskService;

    @PostMapping("/plan/page")
    @ApiOperation(value = "分页")
    public TableDataInfo drillPlanPage(@RequestBody DrillPlanDto dto){
        startPage();
        return getDataTable(drillPlanService.drillPlanListVO(dto));
    }


    @PutMapping("/plan/addOrUpdate")
    @ApiOperation(value = "新增或修改演练计划")
    public AjaxResult saveOrUpdateEntity(@RequestBody DrillPlanDto dto){
        return AjaxResult.success(drillPlanService.insertEntity(dto));
    }

    @PutMapping("/data/addOrUpdate")
    @ApiOperation(value = "新增或修改演练资料")
    public AjaxResult saveEntity(@RequestBody DrillDataDto dto){
        return AjaxResult.success(drillDataService.saveEntity(dto));
    }

    @PutMapping("/review/addOrUpdate")
    @ApiOperation(value = "新增或修改复盘资料")
    public AjaxResult saveEntity(@RequestBody DrillReviewReportDto dto){
        return AjaxResult.success(drillReviewReportService.saveEntity(dto));
    }

    @GetMapping("/data/{id}")
    @ApiOperation(value = "演练资料详情查看")
    public AjaxResult drillDataDetail(@PathVariable Long id){
        return AjaxResult.success(drillPlanService.drillDataDetail(id));
    }

    @GetMapping("/review/{id}")
    @ApiOperation(value = "复盘报告详情查看")
    public AjaxResult DrillReviewReportDetail(@PathVariable Long id){
        return AjaxResult.success(drillPlanService.DrillReviewReportDetail(id));
    }

    @GetMapping("/static")
    @ApiOperation(value = "复盘报告详情查看")
    public AjaxResult DrillReviewReportDetail(){
        return AjaxResult.success(drillPlanService.staticDetail());
    }

    @DeleteMapping("/plan")
    @ApiOperation(value = "实战演练删除")
    public AjaxResult deleteByIds(@RequestBody List<Long> ids){
        return AjaxResult.success(drillPlanService.deleteByIds(ids));
    }

    @PostMapping("/reminder")
    @ApiOperation(value = "提醒")
    public AjaxResult saveEntity(@RequestBody DrillReminderDto dto){
        return AjaxResult.success(drillReminderService.saveEntity(dto));
    }

    @GetMapping("/reminder/{id}")
    @ApiOperation(value = "提醒详情")
    public AjaxResult detail(@PathVariable Long id){
        return AjaxResult.success(drillReminderService.detail(id));
    }

    @PostMapping("/desktop/page")
    @ApiOperation(value = "桌面演练列表")
    public TableDataInfo list(@RequestBody DrillDesktopDto dto){
        startPage();
        return getDataTable(drillDesktopService.list(dto));
    }

    @GetMapping("/desktop/{id}")
    @ApiOperation(value = "桌面演练详情")
    public AjaxResult desktopDetail(@PathVariable Long id){
        return AjaxResult.success(drillDesktopService.detail(id));
    }

    @PutMapping("/desktop")
    @ApiOperation(value = "桌面演练新增或修改")
    public AjaxResult savePlan(@RequestBody DrillDesktopDto dto){
        return AjaxResult.success(drillDesktopService.savePlan(dto));
    }

    @PutMapping("/desktop/task/{id}")
    @ApiOperation(value = "桌面演练任务状态修改")
    public AjaxResult drillDesktopTaskService(@PathVariable Long id){
        return AjaxResult.success(drillDesktopTaskService.updateStatusById(id));
    }

    @DeleteMapping("/desktop")
    @ApiOperation(value = "桌面演练批量删除")
    public AjaxResult deleted(@RequestBody List<Long> ids){
        return AjaxResult.success(drillDesktopService.deleteByIds(ids));
    }

    @PutMapping("/desktop/log")
    @ApiOperation(value = "桌面演练记录新增或修改")
    public AjaxResult saveLog(@RequestBody DrillLogDto dto){
        return AjaxResult.success(drillLogService.saveLog(dto));
    }



    @GetMapping("/desktop/log/{id}")
    @ApiOperation(value = "桌面演练记录")
    public TableDataInfo logList(@PathVariable Long id){
        startPage();
        return getDataTable(drillLogService.logList(id));
    }
}
