package com.tocc.web.controller.system;

import java.util.List;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.tocc.common.annotation.Log;
import com.tocc.common.constant.UserConstants;
import com.tocc.common.core.controller.BaseController;
import com.tocc.common.core.domain.AjaxResult;
import com.tocc.common.core.domain.entity.SysDept;
import com.tocc.common.enums.BusinessType;
import com.tocc.common.utils.StringUtils;
import com.tocc.system.service.ISysDeptService;

/**
 * 部门信息
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system/dept")
public class SysDeptController extends BaseController
{
    @Autowired
    private ISysDeptService deptService;

    /**
     * 获取部门列表
     */
    @PreAuthorize("@ss.hasPermi('system:dept:list')")
    @GetMapping("/list")
    public AjaxResult list(SysDept dept)
    {
        List<SysDept> depts = deptService.selectDeptList(dept);
        return success(depts);
    }

    /**
     * 获取单位列表（包括单位和企业）
     */
    @PreAuthorize("@ss.hasPermi('system:dept:list')")
    @GetMapping("/units")
    public AjaxResult unitList(SysDept dept)
    {
        List<SysDept> units = deptService.selectUnitList(dept);
        return success(units);
    }

    /**
     * 获取部门列表（仅部门类型）
     */
    @PreAuthorize("@ss.hasPermi('system:dept:list')")
    @GetMapping("/departments")
    public AjaxResult departmentList(SysDept dept)
    {
        List<SysDept> departments = deptService.selectDepartmentList(dept);
        return success(departments);
    }

    /**
     * 获取企业列表（仅企业类型）
     */
    @PreAuthorize("@ss.hasPermi('system:dept:list')")
    @GetMapping("/enterprises")
    public AjaxResult enterpriseList(SysDept dept)
    {
        List<SysDept> enterprises = deptService.selectEnterpriseList(dept);
        return success(enterprises);
    }

    /**
     * 获取单位列表（仅单位类型，不包括企业）
     */
    @PreAuthorize("@ss.hasPermi('system:dept:list')")
    @GetMapping("/unitsOnly")
    public AjaxResult unitOnlyList(SysDept dept)
    {
        List<SysDept> units = deptService.selectUnitOnlyList(dept);
        return success(units);
    }

    /**
     * 获取单位树列表（包括单位和企业）
     */
    @PreAuthorize("@ss.hasPermi('system:dept:list')")
    @GetMapping("/unitTree")
    public AjaxResult unitTree(SysDept dept)
    {
        return success(deptService.selectUnitTreeList(dept));
    }

    /**
     * 获取部门树列表（仅部门类型）
     */
    @PreAuthorize("@ss.hasPermi('system:dept:list')")
    @GetMapping("/departmentTree")
    public AjaxResult departmentTree(SysDept dept)
    {
        return success(deptService.selectDepartmentTreeList(dept));
    }

    /**
     * 获取单位-部门层级树形结构
     * 单位作为一级节点，部门作为二级节点
     */
    @PreAuthorize("@ss.hasPermi('system:dept:list')")
    @GetMapping("/unitDeptTree")
    public AjaxResult unitDeptTree(SysDept dept)
    {
        return success(deptService.selectUnitDeptTreeList(dept));
    }

    /**
     * 根据单位ID获取该单位下的所有部门
     */
    @PreAuthorize("@ss.hasPermi('system:dept:list')")
    @GetMapping("/unit/{unitId}/departments")
    public AjaxResult getDepartmentsByUnitId(@PathVariable Long unitId)
    {
        List<SysDept> departments = deptService.selectDepartmentsByUnitId(unitId);
        return success(departments);
    }

    /**
     * 查询部门列表（排除节点）
     */
    @PreAuthorize("@ss.hasPermi('system:dept:list')")
    @GetMapping("/list/exclude/{deptId}")
    public AjaxResult excludeChild(@PathVariable(value = "deptId", required = false) Long deptId)
    {
        List<SysDept> depts = deptService.selectDeptList(new SysDept());
        depts.removeIf(d -> d.getDeptId().intValue() == deptId || ArrayUtils.contains(StringUtils.split(d.getAncestors(), ","), deptId + ""));
        return success(depts);
    }

    /**
     * 根据部门编号获取详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:dept:query')")
    @GetMapping(value = "/{deptId}")
    public AjaxResult getInfo(@PathVariable Long deptId)
    {
        deptService.checkDeptDataScope(deptId);
        return success(deptService.selectDeptById(deptId));
    }

    /**
     * 新增部门
     */
    @PreAuthorize("@ss.hasPermi('system:dept:add')")
    @Log(title = "部门管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody SysDept dept)
    {
        if (!deptService.checkDeptNameUnique(dept))
        {
            return error("新增部门'" + dept.getDeptName() + "'失败，部门名称已存在");
        }
        dept.setCreateBy(getUsername());
        return toAjax(deptService.insertDept(dept));
    }

    /**
     * 修改部门
     */
    @PreAuthorize("@ss.hasPermi('system:dept:edit')")
    @Log(title = "部门管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody SysDept dept)
    {
        Long deptId = dept.getDeptId();
        deptService.checkDeptDataScope(deptId);
        if (!deptService.checkDeptNameUnique(dept))
        {
            return error("修改部门'" + dept.getDeptName() + "'失败，部门名称已存在");
        }
        else if (dept.getParentId().equals(deptId))
        {
            return error("修改部门'" + dept.getDeptName() + "'失败，上级部门不能是自己");
        }
        else if (StringUtils.equals(UserConstants.DEPT_DISABLE, dept.getStatus()) && deptService.selectNormalChildrenDeptById(deptId) > 0)
        {
            return error("该部门包含未停用的子部门！");
        }
        dept.setUpdateBy(getUsername());
        return toAjax(deptService.updateDept(dept));
    }

    /**
     * 删除部门
     */
    @PreAuthorize("@ss.hasPermi('system:dept:remove')")
    @Log(title = "部门管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{deptId}")
    public AjaxResult remove(@PathVariable Long deptId)
    {
        if (deptService.hasChildByDeptId(deptId))
        {
            return warn("存在下级部门,不允许删除");
        }
        if (deptService.checkDeptExistUser(deptId))
        {
            return warn("部门存在用户,不允许删除");
        }
        deptService.checkDeptDataScope(deptId);
        return toAjax(deptService.deleteDeptById(deptId));
    }
}
