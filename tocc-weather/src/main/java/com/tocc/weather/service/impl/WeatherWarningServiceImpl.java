package com.tocc.weather.service.impl;

import java.util.*;
import java.util.stream.Collectors;

import com.tocc.common.utils.StringUtils;
import com.tocc.weather.domain.entity.WeatherWarningNotification;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.BeanUtils;
import com.tocc.common.utils.DateUtils;
import com.tocc.common.utils.SecurityUtils;
import com.tocc.common.exception.ServiceException;
import com.tocc.system.service.ISysDictDataService;
import com.tocc.system.service.ISysUserService;
import com.tocc.common.core.domain.entity.SysUser;
import com.tocc.common.service.IOrganizationService;
import com.tocc.weather.mapper.WeatherWarningMapper;
import com.tocc.weather.mapper.WeatherWarningAreaMapper;
import com.tocc.weather.domain.entity.WeatherWarning;
import com.tocc.weather.domain.entity.WeatherWarningArea;
import com.tocc.weather.domain.dto.WeatherWarningDTO;
import com.tocc.weather.domain.dto.WeatherWarningCreateDTO;
import com.tocc.weather.domain.dto.WeatherWarningAreaDTO;
import com.tocc.weather.domain.vo.WeatherWarningVO;
import com.tocc.weather.domain.vo.WeatherWarningAreaVO;
import com.tocc.weather.domain.vo.WeatherWarningProgressVO;
import com.tocc.weather.service.IWeatherWarningService;
import com.tocc.weather.service.IWeatherWarningNotificationService;
import com.tocc.weather.service.WeatherWarningAlarmService;
import com.tocc.weather.domain.dto.WeatherWarningNotifyTargetDTO;
import com.tocc.weather.constants.WeatherWarningConstants;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 气象预警信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
@Service
public class WeatherWarningServiceImpl implements IWeatherWarningService
{
    private static final Logger log = LoggerFactory.getLogger(WeatherWarningServiceImpl.class);

    @Autowired
    private WeatherWarningMapper weatherWarningMapper;

    @Autowired
    private WeatherWarningAreaMapper weatherWarningAreaMapper;

    @Autowired
    private IWeatherWarningNotificationService notificationService;

    @Autowired
    private ISysDictDataService dictDataService;

    @Autowired
    private WeatherWarningAlarmService weatherWarningAlarmService;

    @Autowired
    private ISysUserService userService;

    @Autowired
    private IOrganizationService organizationService;

    /**
     * 查询气象预警信息
     * 
     * @param warningId 气象预警信息主键
     * @return 气象预警信息
     */
    @Override
    public WeatherWarning selectWeatherWarningByWarningId(String warningId)
    {
        return weatherWarningMapper.selectWeatherWarningByWarningId(warningId);
    }

    /**
     * 查询气象预警信息列表
     * 
     * @param weatherWarningDTO 气象预警信息查询条件
     * @return 气象预警信息
     */
    @Override
    public List<WeatherWarning> selectWeatherWarningList(WeatherWarningDTO weatherWarningDTO)
    {
        List<WeatherWarning> warnings = weatherWarningMapper.selectWeatherWarningList(weatherWarningDTO);
        
        // 为每个预警添加影响区域描述
        for (WeatherWarning warning : warnings) {
            String areaDesc = weatherWarningAreaMapper.selectAreaDescByWarningId(warning.getWarningId());
            warning.setAffectedAreasDesc(areaDesc);
        }
        
        return warnings;
    }

    /**
     * 修改气象预警信息
     * 
     * @param weatherWarning 气象预警信息
     * @return 结果
     */
    @Override
    public int updateWeatherWarning(WeatherWarning weatherWarning)
    {
        weatherWarning.setUpdateTime(DateUtils.getNowDate());
        return weatherWarningMapper.updateWeatherWarning(weatherWarning);
    }

    /**
     * 批量删除气象预警信息
     * 
     * @param warningIds 需要删除的气象预警信息主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteWeatherWarningByWarningIds(String[] warningIds)
    {
        // 删除关联的影响区域
        weatherWarningAreaMapper.deleteWeatherWarningAreaByWarningIds(warningIds);
        
        // 删除关联的通知记录
        notificationService.deleteWeatherWarningNotificationByWarningIds(warningIds);
        
        return weatherWarningMapper.deleteWeatherWarningByWarningIds(warningIds);
    }

    /**
     * 创建预警并发送通知
     * 
     * @param createDTO 创建预警DTO
     * @return 预警ID
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createWarning(WeatherWarningCreateDTO createDTO)
    {
        // 1. 创建预警主记录
        WeatherWarning warning = new WeatherWarning();
        BeanUtils.copyProperties(createDTO, warning);
        warning.setWarningId(UUID.randomUUID().toString());
        warning.setStatus(WeatherWarningConstants.STATUS_ACTIVE); // 有效状态
        warning.setIsNotified(WeatherWarningConstants.NOT_NOTIFIED); // 未通知
        warning.setCreateBy(SecurityUtils.getUsername());
        warning.setCreateTime(new Date());

        weatherWarningMapper.insertWeatherWarning(warning);

        // 2. 创建影响区域记录
        List<WeatherWarningArea> areas = new ArrayList<>();
        for (WeatherWarningAreaDTO areaDto : createDTO.getAffectedAreas()) {
            WeatherWarningArea area = new WeatherWarningArea();
            area.setWarningId(warning.getWarningId());
            area.setRegionId(areaDto.getRegionId());
            area.setRegionName(areaDto.getRegionName());
            area.setCreateTime(new Date());
            areas.add(area);
        }
        if (!areas.isEmpty()) {
            weatherWarningAreaMapper.batchInsertWeatherWarningArea(areas);
        }

        // 3. 创建预警发布告警
        weatherWarningAlarmService.createWarningAlarm(createDTO, warning);

        return warning.getWarningId();
    }

    /**
     * 根据用户ID列表发送预警通知
     *
     * @param warningId 预警ID
     * @param userIds 用户ID列表
     * @return 发送成功的通知数量
     */
    @Override
    @Transactional
    public int sendNotificationsByUserIds(String warningId, List<Long> userIds)
    {
        // 1. 验证预警信息
        WeatherWarning warning = weatherWarningMapper.selectWeatherWarningByWarningId(warningId);
        if (warning == null) {
            throw new ServiceException("预警信息不存在：" + warningId);
        }

        // 2. 根据用户ID查询用户信息
        List<WeatherWarningNotifyTargetDTO> notifyTargets = new ArrayList<>();
        for (Long userId : userIds) {
            // 查询用户信息（这里需要调用用户服务获取用户详细信息）
            WeatherWarningNotifyTargetDTO target = getUserInfoById(userId);
            if (target != null) {
                notifyTargets.add(target);
            }
        }

        if (notifyTargets.isEmpty()) {
            throw new ServiceException("未找到有效的通知用户");
        }

        // 3. 更新预警为已通知状态
        warning.setIsNotified(WeatherWarningConstants.NOTIFIED);
        warning.setUpdateBy(SecurityUtils.getUsername());
        warning.setUpdateTime(new Date());
        weatherWarningMapper.updateWeatherWarning(warning);

        // 4. 创建通知记录
        int notificationCount = notificationService.batchCreateNotifications(warningId, notifyTargets);

        // 5. 创建通知确认告警（按单位分组）
        weatherWarningAlarmService.createNotificationAlarms(warning, notifyTargets);

        return notificationCount;
    }

    /**
     * 根据用户ID获取用户信息
     *
     * @param userId 用户ID
     * @return 通知目标DTO
     */
    private WeatherWarningNotifyTargetDTO getUserInfoById(Long userId)
    {
        try {
            // 查询用户信息
            SysUser user = userService.selectUserById(userId);
            if (user == null) {
                log.warn("用户不存在：userId={}", userId);
                return null;
            }

            // 转换为通知目标DTO
            WeatherWarningNotifyTargetDTO target = new WeatherWarningNotifyTargetDTO();
            target.setContactUserId(userId);
            target.setContactUserName(user.getNickName() != null ? user.getNickName() : user.getUserName());
            target.setContactPhone(user.getPhonenumber());
            target.setDeptId(user.getDeptId());

            // 设置部门名称
            if (user.getDept() != null) {
                target.setDeptName(user.getDept().getDeptName());
                target.setContactDeptName(user.getDept().getDeptName());
            } else {
                target.setDeptName("未知部门");
                target.setContactDeptName("未知部门");
            }

            // 设置单位信息（使用用户的orgId作为单位ID）
            target.setContactUnitId(user.getOrgId() != null ? user.getOrgId().toString() : "0");

            // 查询单位名称（需要根据orgId查询单位表获取单位名称）
            String unitName = organizationService.getUnitNameByOrgId(user.getOrgId());
            target.setContactUnitName(unitName);

            // 设置默认超时时间（黄色预警60分钟）
//            target.setTimeoutMinutes(getTimeoutMinutesByLevel("6"));

            return target;
        } catch (Exception e) {
            log.error("获取用户信息失败：userId={}", userId, e);
            return null;
        }
    }

    /**
     * 获取预警详情（包含统计信息）
     * 根据用户权限控制数据访问：
     * 1. 用户A（创建者）：创建后立即可见，可查看通知进度详情
     * 2. 用户B（被通知者）：只有被通知后才能看到，可以确认
     * 3. 用户B同单位/上级单位：被通知后可以看到预警信息，但只有用户B本人能确认
     *
     * @param warningId 预警ID
     * @return 预警详情VO
     */
    @Override
    public WeatherWarningVO selectWeatherWarningDetail(String warningId)
    {
        // 获取当前用户信息
        Long currentUserId = SecurityUtils.getUserId();
        String currentUsername = SecurityUtils.getUsername();
        Long orgId = SecurityUtils.getLoginUser().getUser().getOrgId();
        String currentOrgId = orgId != null ? orgId.toString() : null;

        log.info("查询预警详情，当前用户：userId={} (type={}), username={}, orgId={}, warningId={}",
                currentUserId, currentUserId.getClass().getSimpleName(), currentUsername, currentOrgId, warningId);

        // 查询预警信息（包含统计）
        WeatherWarning warning = weatherWarningMapper.selectWeatherWarningWithStats(warningId);
        if (warning == null) {
            return null;
        }

        // 权限检查：判断用户是否有权限查看该预警，同时获取用户通知记录
        WeatherWarningNotification userNotification = notificationService.selectWeatherWarningNotificationByIds(warningId, currentUserId);
        boolean hasPermission = checkUserPermissionForWarning(warning, currentUsername, currentUserId, currentOrgId, userNotification);
        if (!hasPermission) {
            log.warn("用户无权限查看预警详情：userId={}, warningId={}", currentUserId, warningId);
            return null;
        }

        // 转换为VO
        WeatherWarningVO vo = new WeatherWarningVO();
        BeanUtils.copyProperties(warning, vo);

        // 设置字典标签
        vo.setWarningTypeLabel(dictDataService.selectDictLabel("weather_warning_type", warning.getWarningType()));
        vo.setWarningLevelLabel(dictDataService.selectDictLabel("alarm_level", warning.getWarningLevel()));
        vo.setStatusLabel(getStatusLabel(warning.getStatus()));

        // 设置是否已通知标签
        vo.setIsNotifiedLabel("1".equals(warning.getIsNotified()) ? "已通知" : "未通知");

        // 查询影响区域
        List<WeatherWarningArea> areas = weatherWarningAreaMapper.selectWeatherWarningAreaList(warningId);
        List<WeatherWarningAreaVO> areaVOs = areas.stream().map(area -> {
            WeatherWarningAreaVO areaVO = new WeatherWarningAreaVO();
            BeanUtils.copyProperties(area, areaVO);
            return areaVO;
        }).collect(Collectors.toList());
        vo.setAffectedAreas(areaVOs);

        // 设置影响区域描述
        String areaDesc = weatherWarningAreaMapper.selectAreaDescByWarningId(warningId);
        vo.setAffectedAreasDesc(areaDesc);

        // 确定用户权限角色和权限标识
        setUserPermissionsForWarning(vo, warning, currentUsername, currentUserId, currentOrgId, userNotification);

        // 根据权限设置通知进展信息
        if ("1".equals(warning.getIsNotified())) {
            // 如果已通知，根据权限决定是否显示通知进展详情
            if (vo.getCanViewProgress()) {
                // 有权限查看通知进度详情
                List<WeatherWarningProgressVO> progressList = notificationService.selectNotificationProgress(warningId);
                vo.setNotificationProgress(progressList);

                // 统计信息
                vo.setTotalNotifications(progressList.size());
                long confirmedCount = progressList.stream().filter(p -> "1".equals(p.getConfirmStatus())).count();
                vo.setConfirmedNotifications((int) confirmedCount);
                vo.setUnconfirmedNotifications(progressList.size() - (int) confirmedCount);
            } else {
                // 无权限查看详细进度，只显示基本统计
                vo.setNotificationProgress(new ArrayList<>());
                // 可以显示总数，但不显示详细列表
                List<WeatherWarningProgressVO> progressList = notificationService.selectNotificationProgress(warningId);
                vo.setTotalNotifications(progressList.size());
                long confirmedCount = progressList.stream().filter(p -> "1".equals(p.getConfirmStatus())).count();
                vo.setConfirmedNotifications((int) confirmedCount);
                vo.setUnconfirmedNotifications(progressList.size() - (int) confirmedCount);
            }
        } else {
            // 未通知的预警
            vo.setTotalNotifications(0);
            vo.setConfirmedNotifications(0);
            vo.setUnconfirmedNotifications(0);
            vo.setNotificationProgress(new ArrayList<>());
        }

        // 设置当前用户的确认状态（如果被通知）
        if (vo.getCanConfirm() && userNotification != null) {
            vo.setCurrentUserConfirmStatus(userNotification.getConfirmStatus());
            vo.setCurrentUserConfirmTime(userNotification.getConfirmTime());
        }

        log.info("预警详情查询成功：warningId={}, userRole={}, canConfirm={}, canViewProgress={}",
                warningId, vo.getAlarmType(), vo.getCanConfirm(), vo.getCanViewProgress());

        return vo;
    }

    /**
     * 更新预警状态
     * 只有超级管理员(admin)和广西交通运输厅(department)角色才能操作
     *
     * @param warningId 预警ID
     * @param status 状态
     * @return 结果
     */
    @Override
    public int updateWeatherWarningStatus(String warningId, String status)
    {
        // 1. 检查用户权限
        if (!hasUpdateStatusPermission()) {
            throw new ServiceException("您没有权限更新预警状态");
        }

        // 2. 验证状态值
        if (!isValidStatus(status)) {
            throw new ServiceException("无效的状态值：" + status);
        }

        // 3. 检查预警是否存在
        WeatherWarning warning = weatherWarningMapper.selectWeatherWarningByWarningId(warningId);
        if (warning == null) {
            throw new ServiceException("预警不存在：" + warningId);
        }

        // 4. 更新状态
        int result = weatherWarningMapper.updateWeatherWarningStatus(warningId, status);

        if (result > 0) {
            log.info("预警状态更新成功：warningId={}, status={}, operator={}",
                    warningId, status, SecurityUtils.getUsername());
        }

        return result;
    }

    /**
     * 查询气象预警详细列表（包含影响区域和通知记录）
     * 根据用户权限过滤预警数据：
     * 1. 用户A（创建者）：创建后立即可见
     * 2. 用户B（被通知者）：只有被通知后才能看到
     * 3. 用户B同单位/上级单位：被通知后可以看到预警信息，但只有用户B本人能确认
     */
    @Override
    public List<WeatherWarningVO> selectWeatherWarningDetailList(WeatherWarningDTO queryDTO)
    {
        Long currentUserId = SecurityUtils.getUserId();
        String currentUsername = SecurityUtils.getUsername();
        Long orgId = SecurityUtils.getLoginUser().getUser().getOrgId();
        String currentOrgId = orgId != null ? orgId.toString() : null;

        log.info("查询预警列表，当前用户：userId={}, username={}, orgId={}", currentUserId, currentUsername, currentOrgId);

        // 1. 查询用户创建的预警（立即可见）
        List<WeatherWarning> createdWarnings = weatherWarningMapper.selectWarningsByCreator(currentUsername);
        Set<String> createdWarningIds = createdWarnings.stream()
                .map(WeatherWarning::getWarningId)
                .collect(Collectors.toSet());
        log.info("用户创建的预警数量：{}", createdWarnings.size());

        // 2. 查询当前用户被通知的预警ID列表
        List<WeatherWarningNotification> userNotifications = notificationService.selectNotificationsByUserId(currentUserId);
        Set<String> notifiedWarningIds = userNotifications.stream()
                .map(WeatherWarningNotification::getWarningId)
                .collect(Collectors.toSet());
        log.info("用户被通知的预警数量：{}", notifiedWarningIds.size());

        // 3. 查询同单位其他人被通知的预警ID列表（如果有orgId）
        Set<String> orgVisibleWarningIds = new HashSet<>();
        if (StringUtils.isNotEmpty(currentOrgId)) {
            List<WeatherWarningNotification> orgNotifications = notificationService.selectNotificationsByOrgId(currentOrgId);
            orgVisibleWarningIds = orgNotifications.stream()
                    .map(WeatherWarningNotification::getWarningId)
                    .collect(Collectors.toSet());
            log.info("单位可见的预警数量：{}", orgVisibleWarningIds.size());
        }

        // 4. 查询当前用户单位及上层单位创建的预警（可以看到完整信息）
        Set<String> orgCreatedWarningIds = new HashSet<>();
        if (StringUtils.isNotEmpty(currentOrgId)) {
            try {
                List<WeatherWarning> orgCreatedWarnings = weatherWarningMapper.selectWarningsByOrgId(currentOrgId);
                orgCreatedWarningIds = orgCreatedWarnings.stream()
                        .map(WeatherWarning::getWarningId)
                        .collect(Collectors.toSet());
                log.info("单位及上层单位创建的预警数量：{}", orgCreatedWarningIds.size());
            } catch (Exception e) {
                log.warn("查询单位创建的预警失败，orgId: {}, error: {}", currentOrgId, e.getMessage());
                // 如果查询失败，继续执行，只是这部分预警看不到
            }
        }

        // 5. 合并所有可见的预警ID
        Set<String> allVisibleWarningIds = new HashSet<>();
        // 添加用户创建的预警
        allVisibleWarningIds.addAll(createdWarningIds);
        log.info("添加用户创建的预警后，可见预警数量：{}", allVisibleWarningIds.size());

        // 添加被通知的预警
        allVisibleWarningIds.addAll(notifiedWarningIds);
        log.info("添加被通知的预警后，可见预警数量：{}", allVisibleWarningIds.size());

        // 添加单位可见的预警
        allVisibleWarningIds.addAll(orgVisibleWarningIds);
        log.info("添加单位可见的预警后，可见预警数量：{}", allVisibleWarningIds.size());

        // 添加单位及上层单位创建的预警
        allVisibleWarningIds.addAll(orgCreatedWarningIds);
        log.info("添加单位创建的预警后，总共可见的预警数量：{}", allVisibleWarningIds.size());

        log.info("最终可见的预警ID列表：{}", allVisibleWarningIds);

        // 5. 如果没有可见的预警，直接返回空列表
        if (allVisibleWarningIds.isEmpty()) {
            return new ArrayList<>();
        }

        // 6. 根据可见的预警ID列表查询预警详细信息
        List<WeatherWarning> warnings = new ArrayList<>();
        for (String warningId : allVisibleWarningIds) {
            WeatherWarning warning = weatherWarningMapper.selectWeatherWarningByWarningId(warningId);
            if (warning != null) {
                // 应用查询条件过滤
                if (matchesQueryConditions(warning, queryDTO)) {
                    warnings.add(warning);
                }
            }
        }

        // 按发布时间倒序排列
        warnings.sort((w1, w2) -> w2.getIssueTime().compareTo(w1.getIssueTime()));

        List<WeatherWarningVO> result = new ArrayList<>();

        for (WeatherWarning warning : warnings) {
            // 7. 转换为VO
            WeatherWarningVO vo = new WeatherWarningVO();
            BeanUtils.copyProperties(warning, vo);

            // 8. 设置字典标签
            vo.setWarningTypeLabel(dictDataService.selectDictLabel("weather_warning_type", warning.getWarningType()));
            vo.setWarningLevelLabel(dictDataService.selectDictLabel("alarm_level", warning.getWarningLevel()));
            vo.setStatusLabel(getStatusLabel(warning.getStatus()));
            vo.setIsNotifiedLabel("1".equals(warning.getIsNotified()) ? "已通知" : "未通知");

            // 9. 确定用户对该预警的权限角色
            String userRole = determineUserRole(warning, currentUsername, createdWarningIds, notifiedWarningIds, orgVisibleWarningIds, orgCreatedWarningIds);
            vo.setAlarmType(userRole); // 使用alarmType字段存储用户角色

            // 10. 设置是否可以确认（只有被直接通知的用户才能确认）
            boolean canConfirm = notifiedWarningIds.contains(warning.getWarningId());
            vo.setCanConfirm(canConfirm);

            // 11. 设置是否可以查看通知进度详情（创建者和单位创建者可以查看）
            boolean canViewProgress = "creator".equals(userRole) || "org_creator".equals(userRole);
            vo.setCanViewProgress(canViewProgress);

            // 12. 查询影响区域
            List<WeatherWarningArea> areas = weatherWarningAreaMapper.selectWeatherWarningAreaList(warning.getWarningId());
            List<WeatherWarningAreaVO> areaVOs = areas.stream().map(area -> {
                WeatherWarningAreaVO areaVO = new WeatherWarningAreaVO();
                BeanUtils.copyProperties(area, areaVO);
                return areaVO;
            }).collect(Collectors.toList());
            vo.setAffectedAreas(areaVOs);

            // 13. 设置影响区域描述
            String areaDesc = weatherWarningAreaMapper.selectAreaDescByWarningId(warning.getWarningId());
            vo.setAffectedAreasDesc(areaDesc);

            // 14. 设置当前用户的确认状态（如果被通知）
            if (canConfirm) {
                WeatherWarningNotification userNotification = userNotifications.stream()
                        .filter(n -> n.getWarningId().equals(warning.getWarningId()))
                        .findFirst()
                        .orElse(null);
                if (userNotification != null) {
                    vo.setCurrentUserConfirmStatus(userNotification.getConfirmStatus());
                    vo.setCurrentUserConfirmTime(userNotification.getConfirmTime());
                }
            }

            result.add(vo);
        }

        log.info("返回预警列表数量：{}", result.size());
        return result;
    }

    /**
     * 确定用户对预警的权限角色
     *
     * @param warning 预警信息
     * @param currentUsername 当前用户名
     * @param createdWarningIds 用户创建的预警ID集合
     * @param notifiedWarningIds 用户被通知的预警ID集合
     * @param orgVisibleWarningIds 单位可见的预警ID集合
     * @param orgCreatedWarningIds 单位及上层单位创建的预警ID集合
     * @return 用户角色：creator-创建者, org_creator-单位创建者, notified-被通知者, org_visible-单位可见
     */
    private String determineUserRole(WeatherWarning warning, String currentUsername,
                                   Set<String> createdWarningIds, Set<String> notifiedWarningIds,
                                   Set<String> orgVisibleWarningIds, Set<String> orgCreatedWarningIds) {
        // 1. 如果是用户本人创建的，优先级最高
        if (currentUsername.equals(warning.getCreateBy()) || createdWarningIds.contains(warning.getWarningId())) {
            return "creator";
        }

        // 2. 如果是单位及上层单位创建的，第二优先级（可以看到完整信息）
        if (orgCreatedWarningIds.contains(warning.getWarningId())) {
            return "org_creator";
        }

        // 3. 如果被直接通知，第三优先级
        if (notifiedWarningIds.contains(warning.getWarningId())) {
            return "notified";
        }

        // 4. 如果是单位可见，最低优先级
        if (orgVisibleWarningIds.contains(warning.getWarningId())) {
            return "org_visible";
        }

        // 5. 默认情况（理论上不应该到这里，因为已经过滤了不可见的预警）
        return "unknown";
    }

    /**
     * 检查预警是否匹配查询条件
     *
     * @param warning 预警信息
     * @param queryDTO 查询条件
     * @return 是否匹配
     */
    private boolean matchesQueryConditions(WeatherWarning warning, WeatherWarningDTO queryDTO) {
        // 预警类型过滤
        if (StringUtils.isNotEmpty(queryDTO.getWarningType()) &&
            !queryDTO.getWarningType().equals(warning.getWarningType())) {
            return false;
        }

        // 预警等级过滤
        if (StringUtils.isNotEmpty(queryDTO.getWarningLevel()) &&
            !queryDTO.getWarningLevel().equals(warning.getWarningLevel())) {
            return false;
        }

        // 状态过滤
        if (StringUtils.isNotEmpty(queryDTO.getStatus()) &&
            !queryDTO.getStatus().equals(warning.getStatus())) {
            return false;
        }

        // 创建者过滤
        if (StringUtils.isNotEmpty(queryDTO.getCreateBy()) &&
            !queryDTO.getCreateBy().equals(warning.getCreateBy())) {
            return false;
        }

        // 发布时间范围过滤
        if (queryDTO.getIssueTimeStart() != null &&
            warning.getIssueTime().before(queryDTO.getIssueTimeStart())) {
            return false;
        }

        if (queryDTO.getIssueTimeEnd() != null &&
            warning.getIssueTime().after(queryDTO.getIssueTimeEnd())) {
            return false;
        }

        // 内容关键字过滤
        if (StringUtils.isNotEmpty(queryDTO.getContentKeyword()) &&
            !warning.getWarningContent().contains(queryDTO.getContentKeyword())) {
            return false;
        }

        // 区域过滤（需要查询关联的区域）
        if (StringUtils.isNotEmpty(queryDTO.getRegionId())) {
            List<WeatherWarningArea> areas = weatherWarningAreaMapper.selectWeatherWarningAreaList(warning.getWarningId());
            boolean hasRegion = areas.stream().anyMatch(area -> queryDTO.getRegionId().equals(area.getRegionId()));
            if (!hasRegion) {
                return false;
            }
        }

        return true;
    }

    /**
     * 检查用户是否有更新预警状态的权限
     * 只有超级管理员(admin)和广西交通运输厅(department)角色才能操作
     *
     * @return 是否有权限
     */
    private boolean hasUpdateStatusPermission() {
        try {
            // 获取当前用户的角色列表
            Set<String> roleKeys = SecurityUtils.getLoginUser().getUser().getRoles().stream()
                    .map(role -> role.getRoleKey())
                    .collect(Collectors.toSet());

            log.info("当前用户角色：{}", roleKeys);

            // 检查是否包含允许的角色
            return roleKeys.contains("admin") || roleKeys.contains("department");

        } catch (Exception e) {
            log.error("检查用户权限失败", e);
            return false;
        }
    }

    /**
     * 验证状态值是否有效
     *
     * @param status 状态值
     * @return 是否有效
     */
    private boolean isValidStatus(String status) {
        return "0".equals(status) || "1".equals(status) || "2".equals(status);
    }

    /**
     * 检查用户是否有权限查看指定预警
     * 权限规则：
     * 1. 预警创建者：立即可见
     * 2. 被通知用户：被通知后可见
     * 3. 同单位/上级单位用户：在有用户被通知后可见
     *
     * @param warning 预警信息
     * @param currentUsername 当前用户名
     * @param currentUserId 当前用户ID
     * @param currentOrgId 当前用户单位ID
     * @param userNotification 用户通知记录（可能为null）
     * @return 是否有权限
     */
    private boolean checkUserPermissionForWarning(WeatherWarning warning, String currentUsername, Long currentUserId, String currentOrgId, WeatherWarningNotification userNotification) {
        try {
            // 1. 检查是否为创建者
            if (currentUsername.equals(warning.getCreateBy())) {
                log.debug("用户是预警创建者，有权限查看：warningId={}", warning.getWarningId());
                return true;
            }

            // 2. 检查是否被通知
            if (userNotification != null) {
                log.debug("用户被通知该预警，有权限查看：warningId={}", warning.getWarningId());
                return true;
            }

            // 3. 检查单位可见性（同单位/上级单位在有用户被通知后可见）
            if (StringUtils.isNotEmpty(currentOrgId)) {
                // 查询该预警是否有本单位的用户被通知
                List<WeatherWarningNotification> orgNotifications = notificationService.selectNotificationsByWarningIdAndOrgId(warning.getWarningId(), currentOrgId);
                if (!orgNotifications.isEmpty()) {
                    log.debug("用户单位有人被通知该预警，有权限查看：warningId={}, orgId={}", warning.getWarningId(), currentOrgId);
                    return true;
                }

                // 查询是否为单位创建的预警（单位及上层单位创建的预警）
                List<WeatherWarning> orgCreatedWarnings = weatherWarningMapper.selectWarningsByOrgId(currentOrgId);
                boolean isOrgCreated = orgCreatedWarnings.stream()
                        .anyMatch(w -> w.getWarningId().equals(warning.getWarningId()));
                if (isOrgCreated) {
                    log.debug("预警由用户单位创建，有权限查看：warningId={}, orgId={}", warning.getWarningId(), currentOrgId);
                    return true;
                }
            }

            log.debug("用户无权限查看预警：warningId={}, userId={}", warning.getWarningId(), currentUserId);
            return false;

        } catch (Exception e) {
            log.error("检查预警权限失败：warningId={}, userId={}", warning.getWarningId(), currentUserId, e);
            return false;
        }
    }

    /**
     * 为预警VO设置用户权限相关字段
     *
     * @param vo 预警VO
     * @param warning 预警信息
     * @param currentUsername 当前用户名
     * @param currentUserId 当前用户ID
     * @param currentOrgId 当前用户单位ID
     * @param userNotification 用户通知记录（可能为null）
     */
    private void setUserPermissionsForWarning(WeatherWarningVO vo, WeatherWarning warning, String currentUsername, Long currentUserId, String currentOrgId, WeatherWarningNotification userNotification) {
        try {
            // 1. 确定用户角色
            String userRole = "viewer"; // 默认为查看者
            boolean isCreator = currentUsername.equals(warning.getCreateBy());
            boolean isNotified = false;
            boolean isOrgCreator = false;

            // 检查是否被通知（使用传入的通知记录）
            if (userNotification != null) {
                isNotified = true;
                log.info("用户被通知该预警：warningId={}, userId={}", warning.getWarningId(), currentUserId);
            } else {
                log.info("用户未被通知该预警：warningId={}, userId={}", warning.getWarningId(), currentUserId);
            }

            // 检查是否为单位创建者
            if (!isCreator && StringUtils.isNotEmpty(currentOrgId)) {
                List<WeatherWarning> orgCreatedWarnings = weatherWarningMapper.selectWarningsByOrgId(currentOrgId);
                isOrgCreator = orgCreatedWarnings.stream()
                        .anyMatch(w -> w.getWarningId().equals(warning.getWarningId()));
            }

            // 确定用户角色
            if (isCreator) {
                userRole = "creator";
            } else if (isOrgCreator) {
                userRole = "org_creator";
            } else if (isNotified) {
                userRole = "notified";
            } else {
                userRole = "org_viewer";
            }

            vo.setAlarmType(userRole);

            // 2. 设置是否可以确认（只有被直接通知的用户才能确认）
            vo.setCanConfirm(isNotified);

            // 3. 设置是否可以查看通知进度详情（创建者和单位创建者可以查看）
            vo.setCanViewProgress(isCreator || isOrgCreator);

            log.debug("设置用户权限：warningId={}, userId={}, userRole={}, canConfirm={}, canViewProgress={}",
                    warning.getWarningId(), currentUserId, userRole, vo.getCanConfirm(), vo.getCanViewProgress());

        } catch (Exception e) {
            log.error("设置用户权限失败：warningId={}, userId={}", warning.getWarningId(), currentUserId, e);
            // 设置默认权限（最小权限）
            vo.setAlarmType("viewer");
            vo.setCanConfirm(false);
            vo.setCanViewProgress(false);
        }
    }

    /**
     * 统计未失效的预警数量（按等级分组）
     * 基于用户权限控制，只统计用户有权限查看的预警
     *
     * @return 预警等级统计结果
     */
    @Override
    public Map<String, Integer> selectWarningLevelStatistics() {
        // 获取当前用户信息
        Long currentUserId = SecurityUtils.getUserId();
        String currentUsername = SecurityUtils.getUsername();
        Long orgId = SecurityUtils.getLoginUser().getUser().getOrgId();
        String currentOrgId = orgId != null ? orgId.toString() : null;

        log.info("统计预警等级数据，当前用户：userId={}, username={}, orgId={}", currentUserId, currentUsername, currentOrgId);

        // 复用 detail-list 的权限控制逻辑，获取用户可见的预警ID列表
        Set<String> allVisibleWarningIds = getVisibleWarningIds(currentUserId, currentUsername, currentOrgId);

        log.info("用户可见的预警数量：{}", allVisibleWarningIds.size());

        // 初始化统计结果
        Map<String, Integer> statistics = new HashMap<>();
        statistics.put("5", 0); // 蓝色预警
        statistics.put("6", 0); // 黄色预警
        statistics.put("7", 0); // 橙色预警
        statistics.put("8", 0); // 红色预警

        // 如果没有可见的预警，直接返回空统计
        if (allVisibleWarningIds.isEmpty()) {
            log.info("用户无可见预警，返回空统计");
            return statistics;
        }

        // 查询未失效的预警并按等级统计
        for (String warningId : allVisibleWarningIds) {
            WeatherWarning warning = weatherWarningMapper.selectWeatherWarningByWarningId(warningId);
            if (warning != null && "0".equals(warning.getStatus())) { // 只统计有效状态的预警
                String warningLevel = warning.getWarningLevel();
                if (statistics.containsKey(warningLevel)) {
                    statistics.put(warningLevel, statistics.get(warningLevel) + 1);
                }
            }
        }

        log.info("预警等级统计结果：蓝色={}, 黄色={}, 橙色={}, 红色={}",
                statistics.get("5"), statistics.get("6"), statistics.get("7"), statistics.get("8"));

        return statistics;
    }

    /**
     * 获取用户可见的预警ID列表
     * 复用 detail-list 接口的权限控制逻辑
     *
     * @param currentUserId 当前用户ID
     * @param currentUsername 当前用户名
     * @param currentOrgId 当前用户单位ID
     * @return 可见的预警ID集合
     */
    private Set<String> getVisibleWarningIds(Long currentUserId, String currentUsername, String currentOrgId) {
        // 1. 查询用户创建的预警（立即可见）
        List<WeatherWarning> createdWarnings = weatherWarningMapper.selectWarningsByCreator(currentUsername);
        Set<String> createdWarningIds = createdWarnings.stream()
                .map(WeatherWarning::getWarningId)
                .collect(Collectors.toSet());

        // 2. 查询当前用户被通知的预警ID列表
        List<WeatherWarningNotification> userNotifications = notificationService.selectNotificationsByUserId(currentUserId);
        Set<String> notifiedWarningIds = userNotifications.stream()
                .map(WeatherWarningNotification::getWarningId)
                .collect(Collectors.toSet());

        // 3. 查询同单位其他人被通知的预警ID列表（如果有orgId）
        Set<String> orgVisibleWarningIds = new HashSet<>();
        if (StringUtils.isNotEmpty(currentOrgId)) {
            List<WeatherWarningNotification> orgNotifications = notificationService.selectNotificationsByOrgId(currentOrgId);
            orgVisibleWarningIds = orgNotifications.stream()
                    .map(WeatherWarningNotification::getWarningId)
                    .collect(Collectors.toSet());
        }

        // 4. 查询当前用户单位及上层单位创建的预警（可以看到完整信息）
        Set<String> orgCreatedWarningIds = new HashSet<>();
        if (StringUtils.isNotEmpty(currentOrgId)) {
            try {
                List<WeatherWarning> orgCreatedWarnings = weatherWarningMapper.selectWarningsByOrgId(currentOrgId);
                orgCreatedWarningIds = orgCreatedWarnings.stream()
                        .map(WeatherWarning::getWarningId)
                        .collect(Collectors.toSet());
            } catch (Exception e) {
                log.warn("查询单位创建的预警失败，orgId: {}, error: {}", currentOrgId, e.getMessage());
                // 如果查询失败，继续执行，只是这部分预警看不到
            }
        }

        // 5. 合并所有可见的预警ID
        Set<String> allVisibleWarningIds = new HashSet<>();
        allVisibleWarningIds.addAll(createdWarningIds);
        allVisibleWarningIds.addAll(notifiedWarningIds);
        allVisibleWarningIds.addAll(orgVisibleWarningIds);
        allVisibleWarningIds.addAll(orgCreatedWarningIds);

        return allVisibleWarningIds;
    }

    /**
     * 根据预警ID查询预警详情（基于权限控制）
     * 参考 detail-list 接口的权限控制逻辑和返回数据结构
     *
     * @param warningId 预警ID
     * @return 预警详情VO，如果无权限或不存在则返回null
     */
    @Override
    public WeatherWarningVO selectWeatherWarningById(String warningId) {
        // 获取当前用户信息
        Long currentUserId = SecurityUtils.getUserId();
        String currentUsername = SecurityUtils.getUsername();
        Long orgId = SecurityUtils.getLoginUser().getUser().getOrgId();
        String currentOrgId = orgId != null ? orgId.toString() : null;

        log.info("查询预警详情，当前用户：userId={}, username={}, orgId={}, warningId={}",
                currentUserId, currentUsername, currentOrgId, warningId);

        // 查询预警基础信息
        WeatherWarning warning = weatherWarningMapper.selectWeatherWarningByWarningId(warningId);
        if (warning == null) {
            log.warn("预警不存在：warningId={}", warningId);
            return null;
        }

        // 获取用户通知记录
        WeatherWarningNotification userNotification = notificationService.selectWeatherWarningNotificationByIds(warningId, currentUserId);

        // 权限检查：判断用户是否有权限查看该预警
        boolean hasPermission = checkUserPermissionForWarning(warning, currentUsername, currentUserId, currentOrgId, userNotification);
        if (!hasPermission) {
            log.warn("用户无权限查看预警详情：userId={}, warningId={}", currentUserId, warningId);
            return null;
        }

        // 转换为VO
        WeatherWarningVO vo = new WeatherWarningVO();
        BeanUtils.copyProperties(warning, vo);

        // 设置字典标签
        vo.setWarningTypeLabel(dictDataService.selectDictLabel("weather_warning_type", warning.getWarningType()));
        vo.setWarningLevelLabel(dictDataService.selectDictLabel("alarm_level", warning.getWarningLevel()));
        vo.setStatusLabel(getStatusLabel(warning.getStatus()));
        vo.setIsNotifiedLabel("1".equals(warning.getIsNotified()) ? "已通知" : "未通知");

        // 确定用户权限角色和权限标识
        setUserPermissionsForWarning(vo, warning, currentUsername, currentUserId, currentOrgId, userNotification);

        // 查询影响区域
        List<WeatherWarningArea> areas = weatherWarningAreaMapper.selectWeatherWarningAreaList(warningId);
        List<WeatherWarningAreaVO> areaVOs = areas.stream().map(area -> {
            WeatherWarningAreaVO areaVO = new WeatherWarningAreaVO();
            BeanUtils.copyProperties(area, areaVO);
            return areaVO;
        }).collect(Collectors.toList());
        vo.setAffectedAreas(areaVOs);

        // 设置影响区域描述
        String areaDesc = weatherWarningAreaMapper.selectAreaDescByWarningId(warningId);
        vo.setAffectedAreasDesc(areaDesc);

        // 设置通知统计信息
        if ("1".equals(warning.getIsNotified())) {
            Map<String, Object> notificationStats = notificationService.selectNotificationStats(warningId);
            if (notificationStats != null) {
                vo.setTotalNotifications(getIntValue(notificationStats, "totalNotifications"));
                vo.setConfirmedNotifications(getIntValue(notificationStats, "confirmedNotifications"));
                vo.setUnconfirmedNotifications(getIntValue(notificationStats, "unconfirmedNotifications"));
                vo.setTimeoutNotifications(getIntValue(notificationStats, "timeoutNotifications"));
            }
        }

        // 设置当前用户的确认状态（如果被通知）
        if (vo.getCanConfirm() && userNotification != null) {
            vo.setCurrentUserConfirmStatus(userNotification.getConfirmStatus());
            vo.setCurrentUserConfirmTime(userNotification.getConfirmTime());
        }

        // 根据权限设置通知进展信息
        if ("1".equals(warning.getIsNotified())) {
            if (vo.getCanViewProgress()) {
                // 有权限查看通知进度详情
                List<WeatherWarningProgressVO> progressList = notificationService.selectNotificationProgress(warningId);
                vo.setNotificationProgress(progressList);
            } else {
                // 无权限查看详细进度，只显示基本统计
                vo.setNotificationProgress(new ArrayList<>());
            }
        }

        log.info("成功查询预警详情：warningId={}, userRole={}", warningId, vo.getAlarmType());
        return vo;
    }

    /**
     * 从Map中安全获取Integer值
     */
    private Integer getIntValue(Map<String, Object> map, String key) {
        Object value = map.get(key);
        if (value instanceof Number) {
            return ((Number) value).intValue();
        }
        return 0;
    }



    /**
     * 获取状态标签
     */
    private String getStatusLabel(String status) {
        switch (status) {
            case "0": return "有效";
            case "1": return "失效";
            case "2": return "取消";
            default: return "未知";
        }
    }
}
