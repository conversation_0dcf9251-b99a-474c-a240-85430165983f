package com.tocc.weather.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;
import java.util.List;

/**
 * 气象预警详情VO
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
public class WeatherWarningVO
{
    /** 预警ID */
    private String warningId;

    /** 预警类型 */
    private String warningType;

    /** 预警类型标签 */
    private String warningTypeLabel;

    /** 预警等级 */
    private String warningLevel;

    /** 预警等级标签 */
    private String warningLevelLabel;

    /** 预警内容 */
    private String warningContent;

    /** 防御指南 */
    private String preventionGuide;

    /** 受影响道路 */
    private String affectedRoads;

    /** 发布时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date issueTime;

    /** 失效时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date expireTime;

    /** 状态 */
    private String status;

    /** 状态标签 */
    private String statusLabel;

    /** 是否已通知 */
    private String isNotified;

    /** 是否已通知标签 */
    private String isNotifiedLabel;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /** 创建者 */
    private String createBy;

    /** 影响区域信息 */
    private List<WeatherWarningAreaVO> affectedAreas;

    /** 影响区域描述 */
    private String affectedAreasDesc;

    /** 通知统计信息 */
    private Integer totalNotifications;
    private Integer confirmedNotifications;
    private Integer unconfirmedNotifications;
    private Integer timeoutNotifications;

    /** 告警类型标识（creator-创建者，notified-被通知者，org_visible-单位可见） */
    private String alarmType;

    /** 是否可以确认（只有被直接通知的用户才能确认） */
    private Boolean canConfirm;

    /** 是否可以查看通知进度详情（创建者和单位创建者可以查看） */
    private Boolean canViewProgress;

    /** 当前用户确认状态（仅通知确认告警时有值） */
    private String currentUserConfirmStatus;

    /** 当前用户确认时间（仅通知确认告警时有值） */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date currentUserConfirmTime;

    /** 通知进展详情列表（仅预警发布告警时有值） */
    private List<WeatherWarningProgressVO> notificationProgress;

    /** 通知状态（0-未通知，1-通知未被全部确认，2-通知已确认） */
    private String notificationStatus;

    /** 确认状态（0-未确认，1-已确认）- 针对当前用户 */
    private String confirmStatus;

    /** 是否可以通知（只有org_id为100的用户才能通知） */
    private Boolean canNotify;

    public String getWarningId() 
    {
        return warningId;
    }

    public void setWarningId(String warningId) 
    {
        this.warningId = warningId;
    }

    public String getWarningType() 
    {
        return warningType;
    }

    public void setWarningType(String warningType) 
    {
        this.warningType = warningType;
    }

    public String getWarningTypeLabel() 
    {
        return warningTypeLabel;
    }

    public void setWarningTypeLabel(String warningTypeLabel) 
    {
        this.warningTypeLabel = warningTypeLabel;
    }

    public String getWarningLevel() 
    {
        return warningLevel;
    }

    public void setWarningLevel(String warningLevel) 
    {
        this.warningLevel = warningLevel;
    }

    public String getWarningLevelLabel() 
    {
        return warningLevelLabel;
    }

    public void setWarningLevelLabel(String warningLevelLabel) 
    {
        this.warningLevelLabel = warningLevelLabel;
    }

    public String getWarningContent() 
    {
        return warningContent;
    }

    public void setWarningContent(String warningContent) 
    {
        this.warningContent = warningContent;
    }

    public String getPreventionGuide() 
    {
        return preventionGuide;
    }

    public void setPreventionGuide(String preventionGuide) 
    {
        this.preventionGuide = preventionGuide;
    }

    public String getAffectedRoads() 
    {
        return affectedRoads;
    }

    public void setAffectedRoads(String affectedRoads) 
    {
        this.affectedRoads = affectedRoads;
    }

    public Date getIssueTime() 
    {
        return issueTime;
    }

    public void setIssueTime(Date issueTime) 
    {
        this.issueTime = issueTime;
    }

    public Date getExpireTime() 
    {
        return expireTime;
    }

    public void setExpireTime(Date expireTime) 
    {
        this.expireTime = expireTime;
    }

    public String getStatus() 
    {
        return status;
    }

    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatusLabel() 
    {
        return statusLabel;
    }

    public void setStatusLabel(String statusLabel)
    {
        this.statusLabel = statusLabel;
    }

    public String getIsNotified()
    {
        return isNotified;
    }

    public void setIsNotified(String isNotified)
    {
        this.isNotified = isNotified;
    }

    public String getIsNotifiedLabel()
    {
        return isNotifiedLabel;
    }

    public void setIsNotifiedLabel(String isNotifiedLabel)
    {
        this.isNotifiedLabel = isNotifiedLabel;
    }

    public Date getCreateTime() 
    {
        return createTime;
    }

    public void setCreateTime(Date createTime) 
    {
        this.createTime = createTime;
    }

    public String getCreateBy() 
    {
        return createBy;
    }

    public void setCreateBy(String createBy) 
    {
        this.createBy = createBy;
    }

    public List<WeatherWarningAreaVO> getAffectedAreas() 
    {
        return affectedAreas;
    }

    public void setAffectedAreas(List<WeatherWarningAreaVO> affectedAreas) 
    {
        this.affectedAreas = affectedAreas;
    }

    public String getAffectedAreasDesc() 
    {
        return affectedAreasDesc;
    }

    public void setAffectedAreasDesc(String affectedAreasDesc) 
    {
        this.affectedAreasDesc = affectedAreasDesc;
    }

    public Integer getTotalNotifications() 
    {
        return totalNotifications;
    }

    public void setTotalNotifications(Integer totalNotifications) 
    {
        this.totalNotifications = totalNotifications;
    }

    public Integer getConfirmedNotifications() 
    {
        return confirmedNotifications;
    }

    public void setConfirmedNotifications(Integer confirmedNotifications) 
    {
        this.confirmedNotifications = confirmedNotifications;
    }

    public Integer getUnconfirmedNotifications() 
    {
        return unconfirmedNotifications;
    }

    public void setUnconfirmedNotifications(Integer unconfirmedNotifications) 
    {
        this.unconfirmedNotifications = unconfirmedNotifications;
    }

    public Integer getTimeoutNotifications() 
    {
        return timeoutNotifications;
    }

    public void setTimeoutNotifications(Integer timeoutNotifications)
    {
        this.timeoutNotifications = timeoutNotifications;
    }

    public String getAlarmType()
    {
        return alarmType;
    }

    public void setAlarmType(String alarmType)
    {
        this.alarmType = alarmType;
    }

    public Boolean getCanConfirm()
    {
        return canConfirm;
    }

    public void setCanConfirm(Boolean canConfirm)
    {
        this.canConfirm = canConfirm;
    }

    public Boolean getCanViewProgress()
    {
        return canViewProgress;
    }

    public void setCanViewProgress(Boolean canViewProgress)
    {
        this.canViewProgress = canViewProgress;
    }

    public String getCurrentUserConfirmStatus()
    {
        return currentUserConfirmStatus;
    }

    public void setCurrentUserConfirmStatus(String currentUserConfirmStatus)
    {
        this.currentUserConfirmStatus = currentUserConfirmStatus;
    }

    public Date getCurrentUserConfirmTime()
    {
        return currentUserConfirmTime;
    }

    public void setCurrentUserConfirmTime(Date currentUserConfirmTime)
    {
        this.currentUserConfirmTime = currentUserConfirmTime;
    }

    public List<WeatherWarningProgressVO> getNotificationProgress()
    {
        return notificationProgress;
    }

    public void setNotificationProgress(List<WeatherWarningProgressVO> notificationProgress)
    {
        this.notificationProgress = notificationProgress;
    }

    public String getNotificationStatus()
    {
        return notificationStatus;
    }

    public void setNotificationStatus(String notificationStatus)
    {
        this.notificationStatus = notificationStatus;
    }

    public String getConfirmStatus()
    {
        return confirmStatus;
    }

    public void setConfirmStatus(String confirmStatus)
    {
        this.confirmStatus = confirmStatus;
    }

    public Boolean getCanNotify()
    {
        return canNotify;
    }

    public void setCanNotify(Boolean canNotify)
    {
        this.canNotify = canNotify;
    }

    @Override
    public String toString() {
        return "WeatherWarningVO{" +
                "warningId='" + warningId + '\'' +
                ", warningType='" + warningType + '\'' +
                ", warningLevel='" + warningLevel + '\'' +
                ", warningContent='" + warningContent + '\'' +
                ", issueTime=" + issueTime +
                ", status='" + status + '\'' +
                '}';
    }
}
