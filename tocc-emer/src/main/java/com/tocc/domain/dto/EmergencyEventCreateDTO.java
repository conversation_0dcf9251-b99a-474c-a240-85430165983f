package com.tocc.domain.dto;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.math.BigDecimal;
import java.util.List;

/**
 * 应急事件创建DTO，用于接收前端新增应急事件的完整数据
 * 包含主表数据和根据事件类型的扩展数据
 * 
 * <AUTHOR>
 */
public class EmergencyEventCreateDTO {

    // ========== 主表数据 ==========
    /** 事件标题 */
    @NotBlank(message = "事件标题不能为空")
    @Size(min = 0, max = 200, message = "事件标题长度不能超过200个字符")
    private String eventTitle;

    /** 事件类型（字典值） */
    @NotBlank(message = "事件类型不能为空")
    private String eventType;

    /** 事故类型（字典值） */
    @NotBlank(message = "事故类型不能为空")
    private String accidentType;

    /** 发生时间 */
    @NotNull(message = "发生时间不能为空")
    private Long occurTime;

    /** 行政区名称 */
    private String administrativeArea;

    /** 行政区ID */
    private String administrativeAreaId;

    /** 事件等级（字典值） */
    private String eventLevel;

    /** 填报人ID */
    private String reporterId;

    /** 上报人ID */
    private String submitterId;

    /** 路段管辖单位ID */
    private String roadManagerUnitId;

    /** 路段管辖单位负责人ID */
    private String roadManagerLeaderId;

    /** 事故详细地址 */
    private String detailedAddress;

    /** 经度 */
    private BigDecimal longitude;

    /** 纬度 */
    private BigDecimal latitude;

    /** 影响范围 */
    private String impactScope;

    /** 事件描述 */
    private String eventDescription;

    /** 事件原因 */
    private String eventCause;

    /** 已采取的应急处置措施 */
    private String emergencyMeasures;

    /** 投入的应急力量 */
    private String emergencyForces;

    /** 需上级应急指挥机构支持事项 */
    private String supportNeeded;

    /** 备注 */
    private String remark;

    // ========== 道路交通事故扩展数据 ==========
    /** 路段编号 */
    private String roadSectionCode;

    /** 开始桩号 */
    private String startStakeNumber;

    /** 结束桩号 */
    private String endStakeNumber;

    /** 方向（字典值） */
    private String direction;

    /** 是否影响通行（Y是 N否） */
    private String trafficAffected;

    /** 事故车型 */
    private String vehicleType;

    /** 预计恢复时间 */
    private Long estimatedRecoveryTime;

    /** 人员伤亡情况（道路） */
    private String roadCasualtySituation;

    /** 影响范围及事态发展趋势 */
    private String impactTrend;

    // ========== 水路交通事故扩展数据 ==========
    /** 航道名称 */
    private String waterwayName;

    /** 船舶名称 */
    private String shipName;

    /** 船舶类型（字典值） */
    private String shipType;

    /** 船舶吨位 */
    private BigDecimal shipTonnage;

    /** 人员伤亡情况（水路） */
    private String waterwayCasualtySituation;

    /** 货物信息 */
    private String cargoInfo;

    /** 环境影响 */
    private String environmentalImpact;

    /** 关联预案ID */
    private String emerPlanId;

    /** 辅助决策说明 */
    private String decisionSupport;

    /** 事件等级与响应说明 */
    private String planLevelJudgment;

    /** 关联的项目运营企业ID列表 */
    private List<String> enterprisePersonnelIds;

    /**
     * 转换为EmergencyEventDTO
     */
    public EmergencyEventDTO toEmergencyEventDTO() {
        EmergencyEventDTO dto = new EmergencyEventDTO();
        dto.setEventTitle(this.eventTitle);
        dto.setEventType(this.eventType);
        dto.setAccidentType(this.accidentType);
        dto.setOccurTime(this.occurTime);
        dto.setAdministrativeArea(this.administrativeArea);
        dto.setAdministrativeAreaId(this.administrativeAreaId);
        dto.setEventLevel(this.eventLevel);
        dto.setReporterId(this.reporterId);
        dto.setSubmitterId(this.submitterId);
        dto.setRoadManagerUnitId(this.roadManagerUnitId);
        dto.setRoadManagerLeaderId(this.roadManagerLeaderId);
        dto.setDetailedAddress(this.detailedAddress);
        dto.setLongitude(this.longitude);
        dto.setLatitude(this.latitude);
        dto.setImpactScope(this.impactScope);
        dto.setEventDescription(this.eventDescription);
        dto.setEventCause(this.eventCause);
        dto.setEmergencyMeasures(this.emergencyMeasures);
        dto.setEmergencyForces(this.emergencyForces);
        dto.setSupportNeeded(this.supportNeeded);
        dto.setRemark(this.remark);
        dto.setStatus("0"); // 默认状态：已上报
        dto.setEmerPlanId(this.emerPlanId);
        dto.setDecisionSupport(this.decisionSupport);
        dto.setPlanLevelJudgment(this.planLevelJudgment);
        return dto;
    }

    /**
     * 转换为道路交通事故扩展DTO
     */
    public EmergencyEventRoadTrafficDTO toRoadTrafficDTO(String eventId) {
        EmergencyEventRoadTrafficDTO dto = new EmergencyEventRoadTrafficDTO();
        dto.setEventId(eventId);
        dto.setRoadSectionCode(this.roadSectionCode);
        dto.setStartStakeNumber(this.startStakeNumber);
        dto.setEndStakeNumber(this.endStakeNumber);
        dto.setDirection(this.direction);
        dto.setTrafficAffected(this.trafficAffected);
        dto.setVehicleType(this.vehicleType);
        dto.setEstimatedRecoveryTime(this.estimatedRecoveryTime);
        dto.setCasualtySituation(this.roadCasualtySituation);
        dto.setImpactTrend(this.impactTrend);
        return dto;
    }

    /**
     * 转换为水路交通事故扩展DTO
     */
    public EmergencyEventWaterwayTrafficDTO toWaterwayTrafficDTO(String eventId) {
        EmergencyEventWaterwayTrafficDTO dto = new EmergencyEventWaterwayTrafficDTO();
        dto.setEventId(eventId);
        dto.setWaterwayName(this.waterwayName);
        dto.setShipName(this.shipName);
        dto.setShipType(this.shipType);
        dto.setShipTonnage(this.shipTonnage);
        dto.setCasualtySituation(this.waterwayCasualtySituation);
        dto.setCargoInfo(this.cargoInfo);
        dto.setEnvironmentalImpact(this.environmentalImpact);
        return dto;
    }

    // Getter and Setter methods
    public String getEventTitle() {
        return eventTitle;
    }

    public void setEventTitle(String eventTitle) {
        this.eventTitle = eventTitle;
    }

    public String getEventType() {
        return eventType;
    }

    public void setEventType(String eventType) {
        this.eventType = eventType;
    }

    public String getAccidentType() {
        return accidentType;
    }

    public void setAccidentType(String accidentType) {
        this.accidentType = accidentType;
    }

    public Long getOccurTime() {
        return occurTime;
    }

    public void setOccurTime(Long occurTime) {
        this.occurTime = occurTime;
    }

    public String getAdministrativeArea() {
        return administrativeArea;
    }

    public void setAdministrativeArea(String administrativeArea) {
        this.administrativeArea = administrativeArea;
    }

    public String getAdministrativeAreaId() {
        return administrativeAreaId;
    }

    public void setAdministrativeAreaId(String administrativeAreaId) {
        this.administrativeAreaId = administrativeAreaId;
    }

    public String getEventLevel() {
        return eventLevel;
    }

    public void setEventLevel(String eventLevel) {
        this.eventLevel = eventLevel;
    }

    public String getReporterId() {
        return reporterId;
    }

    public void setReporterId(String reporterId) {
        this.reporterId = reporterId;
    }

    public String getSubmitterId() {
        return submitterId;
    }

    public void setSubmitterId(String submitterId) {
        this.submitterId = submitterId;
    }

    public String getRoadManagerUnitId() {
        return roadManagerUnitId;
    }

    public void setRoadManagerUnitId(String roadManagerUnitId) {
        this.roadManagerUnitId = roadManagerUnitId;
    }

    public String getRoadManagerLeaderId() {
        return roadManagerLeaderId;
    }

    public void setRoadManagerLeaderId(String roadManagerLeaderId) {
        this.roadManagerLeaderId = roadManagerLeaderId;
    }

    public String getDetailedAddress() {
        return detailedAddress;
    }

    public void setDetailedAddress(String detailedAddress) {
        this.detailedAddress = detailedAddress;
    }

    public BigDecimal getLongitude() {
        return longitude;
    }

    public void setLongitude(BigDecimal longitude) {
        this.longitude = longitude;
    }

    public BigDecimal getLatitude() {
        return latitude;
    }

    public void setLatitude(BigDecimal latitude) {
        this.latitude = latitude;
    }

    public String getImpactScope() {
        return impactScope;
    }

    public void setImpactScope(String impactScope) {
        this.impactScope = impactScope;
    }

    public String getEventDescription() {
        return eventDescription;
    }

    public void setEventDescription(String eventDescription) {
        this.eventDescription = eventDescription;
    }

    public String getEventCause() {
        return eventCause;
    }

    public void setEventCause(String eventCause) {
        this.eventCause = eventCause;
    }

    public String getEmergencyMeasures() {
        return emergencyMeasures;
    }

    public void setEmergencyMeasures(String emergencyMeasures) {
        this.emergencyMeasures = emergencyMeasures;
    }

    public String getEmergencyForces() {
        return emergencyForces;
    }

    public void setEmergencyForces(String emergencyForces) {
        this.emergencyForces = emergencyForces;
    }

    public String getSupportNeeded() {
        return supportNeeded;
    }

    public void setSupportNeeded(String supportNeeded) {
        this.supportNeeded = supportNeeded;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    // 道路交通事故字段的getter/setter
    public String getRoadSectionCode() {
        return roadSectionCode;
    }

    public void setRoadSectionCode(String roadSectionCode) {
        this.roadSectionCode = roadSectionCode;
    }

    public String getStartStakeNumber() {
        return startStakeNumber;
    }

    public void setStartStakeNumber(String startStakeNumber) {
        this.startStakeNumber = startStakeNumber;
    }

    public String getEndStakeNumber() {
        return endStakeNumber;
    }

    public void setEndStakeNumber(String endStakeNumber) {
        this.endStakeNumber = endStakeNumber;
    }

    public String getDirection() {
        return direction;
    }

    public void setDirection(String direction) {
        this.direction = direction;
    }

    public String getTrafficAffected() {
        return trafficAffected;
    }

    public void setTrafficAffected(String trafficAffected) {
        this.trafficAffected = trafficAffected;
    }

    public String getVehicleType() {
        return vehicleType;
    }

    public void setVehicleType(String vehicleType) {
        this.vehicleType = vehicleType;
    }

    public Long getEstimatedRecoveryTime() {
        return estimatedRecoveryTime;
    }

    public void setEstimatedRecoveryTime(Long estimatedRecoveryTime) {
        this.estimatedRecoveryTime = estimatedRecoveryTime;
    }

    public String getRoadCasualtySituation() {
        return roadCasualtySituation;
    }

    public void setRoadCasualtySituation(String roadCasualtySituation) {
        this.roadCasualtySituation = roadCasualtySituation;
    }

    public String getImpactTrend() {
        return impactTrend;
    }

    public void setImpactTrend(String impactTrend) {
        this.impactTrend = impactTrend;
    }

    // 水路交通事故字段的getter/setter
    public String getWaterwayName() {
        return waterwayName;
    }

    public void setWaterwayName(String waterwayName) {
        this.waterwayName = waterwayName;
    }

    public String getShipName() {
        return shipName;
    }

    public void setShipName(String shipName) {
        this.shipName = shipName;
    }

    public String getShipType() {
        return shipType;
    }

    public void setShipType(String shipType) {
        this.shipType = shipType;
    }

    public BigDecimal getShipTonnage() {
        return shipTonnage;
    }

    public void setShipTonnage(BigDecimal shipTonnage) {
        this.shipTonnage = shipTonnage;
    }

    public String getWaterwayCasualtySituation() {
        return waterwayCasualtySituation;
    }

    public void setWaterwayCasualtySituation(String waterwayCasualtySituation) {
        this.waterwayCasualtySituation = waterwayCasualtySituation;
    }

    public String getCargoInfo() {
        return cargoInfo;
    }

    public void setCargoInfo(String cargoInfo) {
        this.cargoInfo = cargoInfo;
    }

    public String getEnvironmentalImpact() {
        return environmentalImpact;
    }

    public void setEnvironmentalImpact(String environmentalImpact) {
        this.environmentalImpact = environmentalImpact;
    }

    public String getDecisionSupport() {
        return decisionSupport;
    }

    public void setDecisionSupport(String decisionSupport) {
        this.decisionSupport = decisionSupport;
    }

    public String getPlanLevelJudgment() {
        return planLevelJudgment;
    }

    public void setPlanLevelJudgment(String planLevelJudgment) {
        this.planLevelJudgment = planLevelJudgment;
    }

    public String getEmerPlanId() {
        return emerPlanId;
    }

    public void setEmerPlanId(String emerPlanId) {
        this.emerPlanId = emerPlanId;
    }

    public List<String> getEnterprisePersonnelIds() {
        return enterprisePersonnelIds;
    }

    public void setEnterprisePersonnelIds(List<String> enterprisePersonnelIds) {
        this.enterprisePersonnelIds = enterprisePersonnelIds;
    }
}
