package com.tocc.domain.entity;

import com.tocc.common.annotation.Excel;
import com.tocc.common.core.domain.BaseEntity;

/**
 * 应急事件关联企业对象 emergency_event_enterprise
 * 
 * <AUTHOR>
 */
public class EmergencyEventEnterprise extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private String id;

    /** 应急事件ID */
    @Excel(name = "应急事件ID")
    private String eventId;

    /** 企业人员信息ID */
    @Excel(name = "企业人员信息ID")
    private String enterprisePersonnelId;

    /** 创建人 */
    @Excel(name = "创建人")
    private String creator;

    /** 更新人 */
    @Excel(name = "更新人")
    private String updater;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getEventId() {
        return eventId;
    }

    public void setEventId(String eventId) {
        this.eventId = eventId;
    }

    public String getEnterprisePersonnelId() {
        return enterprisePersonnelId;
    }

    public void setEnterprisePersonnelId(String enterprisePersonnelId) {
        this.enterprisePersonnelId = enterprisePersonnelId;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public String getUpdater() {
        return updater;
    }

    public void setUpdater(String updater) {
        this.updater = updater;
    }

    @Override
    public String toString() {
        return "EmergencyEventEnterprise{" +
                "id='" + id + '\'' +
                ", eventId='" + eventId + '\'' +
                ", enterprisePersonnelId='" + enterprisePersonnelId + '\'' +
                ", creator='" + creator + '\'' +
                ", updater='" + updater + '\'' +
                '}';
    }
}
