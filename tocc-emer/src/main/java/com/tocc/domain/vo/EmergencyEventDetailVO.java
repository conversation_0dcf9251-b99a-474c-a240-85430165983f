package com.tocc.domain.vo;

import com.tocc.em.domain.EmEnterprisePersonnel;
import java.math.BigDecimal;
import java.util.List;

/**
 * 应急事件详情VO对象，包含扩展信息
 * 
 * <AUTHOR>
 */
public class EmergencyEventDetailVO extends EmergencyEventVO {

    // ========== 道路交通事故扩展信息 ==========
    /** 路段编号 */
    private String roadSectionCode;

    /** 开始桩号 */
    private String startStakeNumber;

    /** 结束桩号 */
    private String endStakeNumber;

    /** 方向（字典值） */
    private String direction;

    /** 方向名称 */
    private String directionName;

    /** 是否影响通行（Y是 N否） */
    private String trafficAffected;

    /** 事故车型 */
    private String vehicleType;

    /** 预计恢复时间 */
    private Long estimatedRecoveryTime;

    /** 人员伤亡情况（道路） */
    private String roadCasualtySituation;

    /** 影响范围及事态发展趋势 */
    private String impactTrend;

    // ========== 水路交通事故扩展信息 ==========
    /** 航道名称 */
    private String waterwayName;

    /** 船舶名称 */
    private String shipName;

    /** 船舶类型（字典值） */
    private String shipType;

    /** 船舶类型名称 */
    private String shipTypeName;

    /** 船舶吨位 */
    private BigDecimal shipTonnage;

    /** 人员伤亡情况（水路） */
    private String waterwayCasualtySituation;

    /** 货物信息 */
    private String cargoInfo;

    /** 环境影响 */
    private String environmentalImpact;

    // ========== 关联企业信息 ==========
    /** 关联的项目运营企业列表 */
    private List<EmEnterprisePersonnel> enterpriseList;

    // Getter and Setter methods for road traffic fields
    public String getRoadSectionCode() {
        return roadSectionCode;
    }

    public void setRoadSectionCode(String roadSectionCode) {
        this.roadSectionCode = roadSectionCode;
    }

    public String getStartStakeNumber() {
        return startStakeNumber;
    }

    public void setStartStakeNumber(String startStakeNumber) {
        this.startStakeNumber = startStakeNumber;
    }

    public String getEndStakeNumber() {
        return endStakeNumber;
    }

    public void setEndStakeNumber(String endStakeNumber) {
        this.endStakeNumber = endStakeNumber;
    }

    public String getDirection() {
        return direction;
    }

    public void setDirection(String direction) {
        this.direction = direction;
    }

    public String getDirectionName() {
        return directionName;
    }

    public void setDirectionName(String directionName) {
        this.directionName = directionName;
    }

    public String getTrafficAffected() {
        return trafficAffected;
    }

    public void setTrafficAffected(String trafficAffected) {
        this.trafficAffected = trafficAffected;
    }

    public String getVehicleType() {
        return vehicleType;
    }

    public void setVehicleType(String vehicleType) {
        this.vehicleType = vehicleType;
    }

    public Long getEstimatedRecoveryTime() {
        return estimatedRecoveryTime;
    }

    public void setEstimatedRecoveryTime(Long estimatedRecoveryTime) {
        this.estimatedRecoveryTime = estimatedRecoveryTime;
    }

    public String getRoadCasualtySituation() {
        return roadCasualtySituation;
    }

    public void setRoadCasualtySituation(String roadCasualtySituation) {
        this.roadCasualtySituation = roadCasualtySituation;
    }

    public String getImpactTrend() {
        return impactTrend;
    }

    public void setImpactTrend(String impactTrend) {
        this.impactTrend = impactTrend;
    }

    // Getter and Setter methods for waterway traffic fields
    public String getWaterwayName() {
        return waterwayName;
    }

    public void setWaterwayName(String waterwayName) {
        this.waterwayName = waterwayName;
    }

    public String getShipName() {
        return shipName;
    }

    public void setShipName(String shipName) {
        this.shipName = shipName;
    }

    public String getShipType() {
        return shipType;
    }

    public void setShipType(String shipType) {
        this.shipType = shipType;
    }

    public String getShipTypeName() {
        return shipTypeName;
    }

    public void setShipTypeName(String shipTypeName) {
        this.shipTypeName = shipTypeName;
    }

    public BigDecimal getShipTonnage() {
        return shipTonnage;
    }

    public void setShipTonnage(BigDecimal shipTonnage) {
        this.shipTonnage = shipTonnage;
    }

    public String getWaterwayCasualtySituation() {
        return waterwayCasualtySituation;
    }

    public void setWaterwayCasualtySituation(String waterwayCasualtySituation) {
        this.waterwayCasualtySituation = waterwayCasualtySituation;
    }

    public String getCargoInfo() {
        return cargoInfo;
    }

    public void setCargoInfo(String cargoInfo) {
        this.cargoInfo = cargoInfo;
    }

    public String getEnvironmentalImpact() {
        return environmentalImpact;
    }

    public void setEnvironmentalImpact(String environmentalImpact) {
        this.environmentalImpact = environmentalImpact;
    }

    public List<EmEnterprisePersonnel> getEnterpriseList() {
        return enterpriseList;
    }

    public void setEnterpriseList(List<EmEnterprisePersonnel> enterpriseList) {
        this.enterpriseList = enterpriseList;
    }
}
