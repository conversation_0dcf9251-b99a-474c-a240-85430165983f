package com.tocc.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.google.gson.Gson;
import com.tocc.common.core.domain.entity.SysUser;
import com.tocc.common.core.domain.model.LoginUser;
import com.tocc.common.enums.LevelEnum;
import com.tocc.common.exception.ServiceException;
import com.tocc.common.utils.SecurityUtils;
import com.tocc.common.utils.StringUtils;
import com.tocc.domain.dto.*;
import com.tocc.domain.vo.EmergencyEventDetailVO;
import com.tocc.domain.vo.EmergencyEventRelationsVo;
import com.tocc.domain.vo.EmergencyEventVO;
import com.tocc.domain.vo.YkTokenVO;
import com.tocc.em.domain.EmPrePlanDept;
import com.tocc.em.dto.EmPrePlanDeptDTO;
import com.tocc.em.service.IEmPrePlanDeptService;
import com.tocc.mapper.EmergencyEventMapper;
import com.tocc.mapper.EmergencyEventRoadTrafficMapper;
import com.tocc.mapper.EmergencyEventWaterwayTrafficMapper;
import com.tocc.mapper.EmergencyEventEnterpriseMapper;
import com.tocc.service.IAlarmService;
import com.tocc.service.IEmergencyEventRelationsService;
import com.tocc.service.IEmergencyEventService;
import com.tocc.domain.entity.EmergencyEventEnterprise;
import com.tocc.em.domain.EmEnterprisePersonnel;
import com.tocc.system.domain.vo.ExpertInfoVO;
import com.tocc.system.service.ISysUserService;
import com.tocc.system.service.ISysConfigService;
import com.tocc.utils.HttpClientUtils;
import lombok.SneakyThrows;
import org.apache.poi.xwpf.usermodel.*;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTP;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTPPr;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTSpacing;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.STLineSpacingRule;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTR;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTRPr;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTString;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigInteger;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.MessageFormat;
import java.time.Instant;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;
import java.util.ArrayList;

/**
 * 应急事件Service业务层处理
 *
 * <AUTHOR>
 */
@Service
public class EmergencyEventServiceImpl implements IEmergencyEventService {

    public static final Logger log = LoggerFactory.getLogger(EmergencyEventServiceImpl.class);

    @Autowired
    private EmergencyEventMapper emergencyEventMapper;

    @Autowired
    private EmergencyEventRoadTrafficMapper roadTrafficMapper;

    @Autowired
    private EmergencyEventWaterwayTrafficMapper waterwayTrafficMapper;

    @Autowired
    private EmergencyEventEnterpriseMapper emergencyEventEnterpriseMapper;

    @Autowired
    private IAlarmService alarmService;

    @Autowired
    private ISysUserService userService;

    @Resource
    private IEmergencyEventRelationsService emergencyEventRelationsService ;

    @Resource
    private IEmPrePlanDeptService iEmPrePlanDeptService;

    @Autowired
    private ISysConfigService sysConfigService;

    /**
     * 查询应急事件
     *
     * @param eventId 应急事件主键
     * @return 应急事件
     */
    @Override
    public EmergencyEventVO selectEmergencyEventByEventId(String eventId) {
        EmergencyEventDTO dto = emergencyEventMapper.selectEmergencyEventByEventId(eventId);
        if (dto == null) {
            return null;
        }
        return convertToVO(dto);
    }

    /**
     * 查询应急事件详情（包含扩展信息）
     *
     * @param eventId 应急事件主键
     * @return 应急事件详情
     */
    @Override
    public EmergencyEventDetailVO selectEmergencyEventDetailByEventId(String eventId) {
        EmergencyEventDTO dto = emergencyEventMapper.selectEmergencyEventByEventId(eventId);
        if (dto == null) {
            return null;
        }

        EmergencyEventDetailVO detailVO = new EmergencyEventDetailVO();
        BeanUtils.copyProperties(convertToVO(dto), detailVO);

        // 根据事件类型查询扩展信息
        if ("1".equals(dto.getEventType())) { // 道路交通事故
            EmergencyEventRoadTrafficDTO roadTrafficDTO = roadTrafficMapper.selectEmergencyEventRoadTrafficByEventId(eventId);
            if (roadTrafficDTO != null) {
                detailVO.setRoadSectionCode(roadTrafficDTO.getRoadSectionCode());
                detailVO.setStartStakeNumber(roadTrafficDTO.getStartStakeNumber());
                detailVO.setEndStakeNumber(roadTrafficDTO.getEndStakeNumber());
                detailVO.setDirection(roadTrafficDTO.getDirection());
                detailVO.setTrafficAffected(roadTrafficDTO.getTrafficAffected());
                detailVO.setVehicleType(roadTrafficDTO.getVehicleType());
                detailVO.setEstimatedRecoveryTime(roadTrafficDTO.getEstimatedRecoveryTime());
                detailVO.setRoadCasualtySituation(roadTrafficDTO.getCasualtySituation());
                detailVO.setImpactTrend(roadTrafficDTO.getImpactTrend());
                // TODO: 设置字典名称
            }
        } else if ("2".equals(dto.getEventType())) { // 水路交通事故
            EmergencyEventWaterwayTrafficDTO waterwayTrafficDTO = waterwayTrafficMapper.selectEmergencyEventWaterwayTrafficByEventId(eventId);
            if (waterwayTrafficDTO != null) {
                detailVO.setWaterwayName(waterwayTrafficDTO.getWaterwayName());
                detailVO.setShipName(waterwayTrafficDTO.getShipName());
                detailVO.setShipType(waterwayTrafficDTO.getShipType());
                detailVO.setShipTonnage(waterwayTrafficDTO.getShipTonnage());
                detailVO.setWaterwayCasualtySituation(waterwayTrafficDTO.getCasualtySituation());
                detailVO.setCargoInfo(waterwayTrafficDTO.getCargoInfo());
                detailVO.setEnvironmentalImpact(waterwayTrafficDTO.getEnvironmentalImpact());
                // TODO: 设置字典名称
            }
        }

        // 查询关联的企业信息
        List<EmEnterprisePersonnel> enterpriseList = emergencyEventEnterpriseMapper.selectEnterpriseListByEventId(eventId);
        detailVO.setEnterpriseList(enterpriseList);

        return detailVO;
    }

    /**
     * 查询应急事件列表
     *
     * @param emergencyEvent 应急事件
     * @return 应急事件
     */
    @Override
    public List<EmergencyEventVO> selectEmergencyEventList(EmergencyEventDTO emergencyEvent) {
        // 从SecurityUtils获取当前用户ID
        String currentUserId = getCurrentUserId();

        // 创建查询参数，只查询当前用户作为上报人或填报人的事件
        EmergencyEventDTO queryParam = new EmergencyEventDTO();
        // 复制原有查询条件
        BeanUtils.copyProperties(emergencyEvent, queryParam);
        // 设置当前用户ID用于权限过滤
        queryParam.setReporterId(currentUserId);

        // 直接返回包含用户详细信息的VO列表
        return emergencyEventMapper.selectEmergencyEventListByUser(queryParam);
    }

    /**
     * 新增应急事件
     *
     * @param createDTO 应急事件
     * @return 结果
     */
    @Override
    @Transactional
    public int insertEmergencyEvent(EmergencyEventCreateDTO createDTO) {
        // 生成事件ID
        String eventId = UUID.randomUUID().toString();

        // 转换为主表DTO并设置系统字段
        EmergencyEventDTO eventDTO = createDTO.toEmergencyEventDTO();
        eventDTO.setEventId(eventId);
        Long time = System.currentTimeMillis() / 1000;
        eventDTO.setCreateTime(time);
        // 设置创建者ID（从当前登录用户获取）
        String currentUserId = getCurrentUserId();
        eventDTO.setCreaterId(currentUserId);

        // 如果没有指定填报人，使用当前用户
        if (createDTO.getReporterId() == null) {
//            eventDTO.setReporterId(currentUserId);
            eventDTO.setReporterId("1");
        }
        // TODO: 辅助决策、预案与事件等级判别说明、关联预案
        eventDTO.setEventLevel("2");
        eventDTO.setEmerPlanId("054908b4c5de4b71906875c7fe34a47c");
        eventDTO.setPlanLevelJudgment("根据《广西壮族自治区公路交通突发事件应急预案》，该预案适用于自治区范围内发生的Ⅱ级及以上公路交通突发事件。当国道、省道、高速公路发生交通中断，且抢修时间预计超过24小时时，应启动Ⅱ级应急响应。本次事件涉及泉南高速柳州段因山体塌方造成交通中断，伴随槽罐车粗苯泄漏和多车连环事故，抢险难度大、处置时间长，符合Ⅱ级响应启动条件。");
        eventDTO.setDecisionSupport("该事故发生在由广西高速公路管理有限公司柳州分公司负责的高速公路路段，已判定为重大公路交通突发事件。根据《广西壮族自治区公路交通突发事件应急预案》，符合Ⅱ级响应启动条件，建议启动Ⅱ级应急响应，由自治区交通运输厅统一指挥和调度。推荐派遣危化品处置专家、隧道工程专家及应急救援专家共同参与，确保高效处置。\n" +
                "根据事故现场山体塌方、危化品粗苯泄漏及多车人员被困等特点，需快速开展清障、救援和危化品转运处置工作。建议调配：挖掘机2台、装载机2台、运输车4辆；消防车1辆、救护车2辆、通讯保障车1辆；空载苯槽车1辆、单兵系统1套、无人机设备1套；具体配置可视现场情况动态调整。");
        // 插入主表
        int result = emergencyEventMapper.insertEmergencyEvent(eventDTO);

        if (result > 0) {
            // 根据事件类型插入扩展表
            if ("1".equals(createDTO.getEventType())) { // 道路交通事故
                EmergencyEventRoadTrafficDTO roadTrafficDTO = createDTO.toRoadTrafficDTO(eventId);
                roadTrafficDTO.setId(UUID.randomUUID().toString());
                roadTrafficDTO.setCreateTime(System.currentTimeMillis() / 1000);
                // 设置创建者ID
                roadTrafficDTO.setCreaterId(currentUserId);
                roadTrafficMapper.insertEmergencyEventRoadTraffic(roadTrafficDTO);
            } else if ("2".equals(createDTO.getEventType())) { // 水路交通事故
                EmergencyEventWaterwayTrafficDTO waterwayTrafficDTO = createDTO.toWaterwayTrafficDTO(eventId);
                waterwayTrafficDTO.setId(UUID.randomUUID().toString());
                waterwayTrafficDTO.setCreateTime(System.currentTimeMillis() / 1000);
                // 设置创建者ID
                waterwayTrafficDTO.setCreaterId(currentUserId);
                waterwayTrafficMapper.insertEmergencyEventWaterwayTraffic(waterwayTrafficDTO);
            }

            // 创建告警记录
            createEmergencyEventAlarm(createDTO, eventDTO);

            // 保存企业关联关系
            saveEventEnterpriseRelations(eventId, createDTO.getEnterprisePersonnelIds(), currentUserId);

            // 发送短信通知上报人
            sendSmsToSubmitter(createDTO, eventDTO);
        }

        return result;
    }

    /**
     * 修改应急事件
     *
     * @param createDTO 应急事件
     * @return 结果
     */
    @Override
    @Transactional
    public int updateEmergencyEvent(EmergencyEventCreateDTO createDTO) {
        // 转换为主表DTO并设置系统字段
        EmergencyEventDTO eventDTO = createDTO.toEmergencyEventDTO();
        eventDTO.setUpdateTime(System.currentTimeMillis() / 1000);
        // 设置更新者ID（从当前登录用户获取）
        String currentUserId = getCurrentUserId();
        eventDTO.setUpdaterId(currentUserId);

        // 更新主表
        int result = emergencyEventMapper.updateEmergencyEvent(eventDTO);

        if (result > 0) {
            String eventId = eventDTO.getEventId();

            // 根据事件类型更新扩展表
            if ("1".equals(createDTO.getEventType())) { // 道路交通事故
                // 先删除原有扩展数据
                waterwayTrafficMapper.deleteEmergencyEventWaterwayTrafficByEventId(eventId);

                // 插入或更新道路交通事故扩展数据
                EmergencyEventRoadTrafficDTO existingRoadTraffic = roadTrafficMapper.selectEmergencyEventRoadTrafficByEventId(eventId);
                EmergencyEventRoadTrafficDTO roadTrafficDTO = createDTO.toRoadTrafficDTO(eventId);
                roadTrafficDTO.setUpdateTime(System.currentTimeMillis() / 1000);

                if (existingRoadTraffic != null) {
                    roadTrafficDTO.setId(existingRoadTraffic.getId());
                    roadTrafficMapper.updateEmergencyEventRoadTraffic(roadTrafficDTO);
                } else {
                    roadTrafficDTO.setId(UUID.randomUUID().toString());
                    roadTrafficDTO.setCreateTime(System.currentTimeMillis() / 1000);
                    roadTrafficMapper.insertEmergencyEventRoadTraffic(roadTrafficDTO);
                }
            } else if ("2".equals(createDTO.getEventType())) { // 水路交通事故
                // 先删除原有扩展数据
                roadTrafficMapper.deleteEmergencyEventRoadTrafficByEventId(eventId);

                // 插入或更新水路交通事故扩展数据
                EmergencyEventWaterwayTrafficDTO existingWaterwayTraffic = waterwayTrafficMapper.selectEmergencyEventWaterwayTrafficByEventId(eventId);
                EmergencyEventWaterwayTrafficDTO waterwayTrafficDTO = createDTO.toWaterwayTrafficDTO(eventId);
                waterwayTrafficDTO.setUpdateTime(System.currentTimeMillis() / 1000);

                if (existingWaterwayTraffic != null) {
                    waterwayTrafficDTO.setId(existingWaterwayTraffic.getId());
                    waterwayTrafficMapper.updateEmergencyEventWaterwayTraffic(waterwayTrafficDTO);
                } else {
                    waterwayTrafficDTO.setId(UUID.randomUUID().toString());
                    waterwayTrafficDTO.setCreateTime(System.currentTimeMillis() / 1000);
                    waterwayTrafficMapper.insertEmergencyEventWaterwayTraffic(waterwayTrafficDTO);
                }
            }

            // 更新企业关联关系
            updateEventEnterpriseRelations(eventId, createDTO.getEnterprisePersonnelIds(), currentUserId);
        }

        return result;
    }

    /**
     * 批量删除应急事件
     *
     * @param eventIds 需要删除的应急事件主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteEmergencyEventByEventIds(String[] eventIds) {
        // 删除扩展表数据
        for (String eventId : eventIds) {
            roadTrafficMapper.deleteEmergencyEventRoadTrafficByEventId(eventId);
            waterwayTrafficMapper.deleteEmergencyEventWaterwayTrafficByEventId(eventId);
        }

        // 删除主表数据
        return emergencyEventMapper.deleteEmergencyEventByEventIds(eventIds);
    }

    /**
     * 删除应急事件信息
     *
     * @param eventId 应急事件主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteEmergencyEventByEventId(String eventId) {
        // 删除扩展表数据
        roadTrafficMapper.deleteEmergencyEventRoadTrafficByEventId(eventId);
        waterwayTrafficMapper.deleteEmergencyEventWaterwayTrafficByEventId(eventId);
        // 删除企业关联数据
        emergencyEventEnterpriseMapper.deleteEmergencyEventEnterpriseByEventId(eventId);

        // 删除主表数据
        return emergencyEventMapper.deleteEmergencyEventByEventId(eventId);
    }

    /**
     * 更新事件状态
     *
     * @param eventId 事件ID
     * @param status  新状态
     * @return 结果
     */
    @Override
    public int updateEventStatus(String eventId, String status) {
        Long updateTime = System.currentTimeMillis() / 1000;
        // 获取当前登录用户ID
        String updaterId = getCurrentUserId();
        return emergencyEventMapper.updateEventStatus(eventId, status, updaterId, updateTime);
    }

    /**
     * 根据事件类型和时间范围查询事件列表
     *
     * @param eventType 事件类型
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 事件列表
     */
    @Override
    public List<EmergencyEventVO> selectEventsByTypeAndTime(String eventType, Long startTime, Long endTime) {
        List<EmergencyEventDTO> dtoList = emergencyEventMapper.selectEventsByTypeAndTime(eventType, startTime, endTime);
        return dtoList.stream().map(this::convertToVO).collect(Collectors.toList());
    }

    /**
     * 根据状态查询事件列表
     *
     * @param status 状态
     * @return 事件列表
     */
    @Override
    public List<EmergencyEventVO> selectEventsByStatus(String status) {
        List<EmergencyEventDTO> dtoList = emergencyEventMapper.selectEventsByStatus(status);
        return dtoList.stream().map(this::convertToVO).collect(Collectors.toList());
    }

    /**
     * 统计应急事件数量
     *
     * @param emergencyEvent 查询条件
     * @return 统计数量
     */
    @Override
    public int countEmergencyEvent(EmergencyEventDTO emergencyEvent) {
        return emergencyEventMapper.countEmergencyEvent(emergencyEvent);
    }

    @Override
    public void downloadNotice(HttpServletResponse response, String eventId, String eventLevel) {
        log.info("🎬🎬🎬 === downloadNotice方法开始执行，eventId: {} === 🎬🎬🎬", eventId);

        if (null == eventLevel || StringUtils.isEmpty(eventLevel)) {
            throw new ServiceException("请输入事件级别");
        }
        //查询数据
        EmergencyEventDetailVO vo = this.selectEmergencyEventDetailByEventId(eventId);
        if (ObjectUtil.isNull(vo)) {
            throw new ServiceException("没有找到符合条件的应急事件");
        }
        //准备模板数据
        Map<String, String> templateData = prepareTemplateData(vo);
        try {
            //设置响应头
            String fileName = "关于成立应对" + vo.getEventTitle() + "应急处置工作领导小组的通知" + ".docx";
            String encodedFileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8.name()).replaceAll("\\+", "%20");
            response.setContentType("application/vnd.openxmlformats-officedocument.wordprocessingml.document");
            response.setHeader("Content-Disposition", "attachment; filename*=UTF-8''" + encodedFileName);

            //加载模板文件
            InputStream templateStream = new ClassPathResource("templates/notice_template.docx").getInputStream();
            try (XWPFDocument document = new XWPFDocument(templateStream)) {

                // 替换普通文本内容（但不包括特殊的占位符）
                Map<String, String> basicData = new HashMap<>(templateData);
                basicData.remove("${organization}"); // 只移除需要特殊处理的organization占位符
                log.info("📞 === 处理replaceInParagraph方法 start === 📞");
                for (XWPFParagraph paragraph : document.getParagraphs()) {
                    replaceInParagraph(paragraph, basicData);
                }
                log.info("📞 === 处理replaceInParagraph方法 end=== 📞");

                // 按顺序处理特殊占位符
                log.info("📞 === 即将调用processSpecialPlaceholders === 📞");
                processSpecialPlaceholders(document, vo, templateData,eventLevel,eventId);
                log.info("📞 === processSpecialPlaceholders调用完成 === 📞");

                // 为全文设置首行缩进2个字符
                log.info("🔧 === 开始为全文设置首行缩进2个字符 === 🔧");
                setGlobalFirstLineIndent(document);
                log.info("🔧 === 全文首行缩进设置完成 === 🔧");

                // 输出文档
                log.info("📄 === 开始输出文档到response === 📄");
                document.write(response.getOutputStream());
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException("导出失败");
        }
    }

    /**
     * 替换应急指挥机构内容，保持格式
     */
    private void replaceOrganizationContent(XWPFDocument document, List<EmPrePlanDeptDTO> deptList) {
            for (XWPFParagraph paragraph : document.getParagraphs()) {
            String text = paragraph.getText();
            if (text != null && text.contains("${organization}")) {
                // 找到包含占位符的段落
                replaceOrganizationInParagraph(document, paragraph, deptList);
                break;
            }
        }
    }

    /**
     * 在段落中替换应急指挥机构内容
     */
    private void replaceOrganizationInParagraph(XWPFDocument document, XWPFParagraph paragraph, List<EmPrePlanDeptDTO> deptList) {
        // 获取段落在文档中的位置
        int paragraphIndex = document.getParagraphs().indexOf(paragraph);

        // 清除原段落内容
        for (int i = paragraph.getRuns().size() - 1; i >= 0; i--) {
            paragraph.removeRun(i);
        }

        // 移除原段落
        document.removeBodyElement(paragraphIndex);

        // 插入格式化的应急指挥机构内容
        insertFormattedOrganization(document, deptList);
    }

    /**
     * 在组织机构后插入专家信息
     */
    private void insertExpertInfoAfterOrganization(XWPFDocument document, List<ExpertInfoVO> expertList) {
        log.info("🎯 开始在组织机构后插入专家信息...");
        
        // 查找组织机构的结束位置
        List<XWPFParagraph> paragraphs = document.getParagraphs();
        int insertIndex = findOrganizationEndIndex(paragraphs);
        
        if (insertIndex == -1) {
            log.warn("⚠️ 未找到组织机构结束位置，在文档末尾添加专家信息");
            insertIndex = paragraphs.size();
        }
        
        log.info("📍 在索引 {} 位置插入专家信息", insertIndex);
        
        // 创建专家标题
        XWPFParagraph expertTitleParagraph = document.createParagraph();
        moveToPosition(document, expertTitleParagraph, insertIndex);
        
        expertTitleParagraph.setAlignment(ParagraphAlignment.LEFT);
        setParagraphSpacing(expertTitleParagraph, 12, 6); // 段前12磅，段后6磅
        setFirstLineIndent(expertTitleParagraph, 640); // 首行缩进2个字符
        
        XWPFRun expertTitleRun = expertTitleParagraph.createRun();
        expertTitleRun.setText("专家：");
        expertTitleRun.setFontFamily("仿宋");
        expertTitleRun.setFontSize(16);
        expertTitleRun.setBold(false);
        
        insertIndex++;
        
        // 创建专家内容段落 - 所有专家信息放在一个段落中
        XWPFParagraph expertContentParagraph = document.createParagraph();
        moveToPosition(document, expertContentParagraph, insertIndex);
        
        expertContentParagraph.setAlignment(ParagraphAlignment.LEFT);
        setParagraphSpacing(expertContentParagraph, 0, 3); // 段前0磅，段后3磅
        setFirstLineIndent(expertContentParagraph, 960); // 缩进3个字符，形成层次感
        
        XWPFRun expertContentRun = expertContentParagraph.createRun();
        
        // 构建所有专家信息文本
        StringBuilder allExpertsText = new StringBuilder();
        
        for (int i = 0; i < expertList.size(); i++) {
            ExpertInfoVO expert = expertList.get(i);
            
            if (i > 0) {
                allExpertsText.append("，"); // 多个专家用逗号分隔
            }
            
            // 专家姓名
            allExpertsText.append(expert.getName() != null ? expert.getName() : "");
            
            // 构建专家详细信息：(从事专业,职称)
            StringBuilder expertDetails = new StringBuilder();
            
            // 添加从事专业（擅长专业）
            if (expert.getSpecialtyField() != null && !expert.getSpecialtyField().isEmpty()) {
                expertDetails.append(expert.getSpecialtyField());
            }
            
            // 添加职称
            if (expert.getProfessionalTitle() != null && !expert.getProfessionalTitle().isEmpty()) {
                if (expertDetails.length() > 0) {
                    expertDetails.append("，");
                }
                expertDetails.append(expert.getProfessionalTitle());
            }
            
            // 如果有详细信息，加上括号
            if (expertDetails.length() > 0) {
                allExpertsText.append("（").append(expertDetails.toString()).append("）");
            }
            
            // 添加联系电话
            if (expert.getPhone() != null && !expert.getPhone().isEmpty()) {
                allExpertsText.append(" ").append(expert.getPhone());
            }
            
            log.info("✅ 已添加专家: {} - 专业: {} - 职称: {} - 电话: {}", 
                    expert.getName(), expert.getSpecialtyField(), expert.getProfessionalTitle(), expert.getPhone());
        }
        
        expertContentRun.setText(allExpertsText.toString());
        expertContentRun.setFontFamily("仿宋");
        expertContentRun.setFontSize(16);
        expertContentRun.setBold(false);
        
        log.info("🎯 专家信息插入完成，共添加 {} 位专家", expertList.size());
    }
    
    /**
     * 查找组织机构的结束位置
     */
    private int findOrganizationEndIndex(List<XWPFParagraph> paragraphs) {
        for (int i = paragraphs.size() - 1; i >= 0; i--) {
            String text = paragraphs.get(i).getText();
            if (text != null && isOrganizationContent(text)) {
                return i + 1; // 返回组织机构内容后的位置
            }
        }
        return -1;
    }
    
    /**
     * 将段落移动到指定位置
     */
    private void moveToPosition(XWPFDocument document, XWPFParagraph paragraph, int targetIndex) {
        // 获取当前段落的位置
        List<XWPFParagraph> paragraphs = document.getParagraphs();
        int currentIndex = paragraphs.indexOf(paragraph);
        
        if (currentIndex == -1 || currentIndex == targetIndex) {
            return; // 段落不存在或已在目标位置
        }
        
        // Word文档的段落移动比较复杂，这里采用简单的方法
        // 实际上由于我们是按顺序创建的，通常不需要移动
        log.debug("段落已在位置 {}, 目标位置 {}", currentIndex, targetIndex);
    }

    /**
     * 插入格式化的应急指挥机构内容
     */
    private void insertFormattedOrganization(XWPFDocument document, List<EmPrePlanDeptDTO> deptList) {
        if (deptList == null || deptList.isEmpty()) {
            log.warn("组织机构数据为空，跳过处理");
            return;
        }

        log.info("🏗️ 开始插入格式化的组织机构内容，树形根节点数量: {}", deptList.size());

        // 清理"现将有关事项通知如下"后面的空段落
        cleanEmptyParagraphsAfterNotice(document);

        // 动态计算与"现将有关事项通知如下"的距离
        int dynamicSpacing = calculateSpacingFromNotice(document);
        
        // 添加主标题"一、应急指挥机构"
        XWPFParagraph mainTitleParagraph = document.createParagraph();
        mainTitleParagraph.setStyle("2"); // 设置为标题2
        mainTitleParagraph.setAlignment(ParagraphAlignment.LEFT); // 左对齐
        setParagraphSpacing(mainTitleParagraph, dynamicSpacing, 6); // 段前28磅，段后6磅
        setFirstLineIndent(mainTitleParagraph, 640); // 设置首行缩进2个字符（640 twips）
        XWPFRun mainTitleRun = mainTitleParagraph.createRun();
        mainTitleRun.setText("一、应急指挥机构");
        mainTitleRun.setFontFamily("方正小标宋简体");
        mainTitleRun.setFontSize(22); // 二号字体约22磅
        mainTitleRun.setBold(true);

        // 使用递归方法处理树形结构
        for (int i = 0; i < deptList.size(); i++) {
            EmPrePlanDeptDTO rootDept = deptList.get(i);
            log.info("📋 处理根节点[{}]: {}", i + 1, rootDept.getDeptName());
            processOrganizationTreeNode(document, rootDept, 0, i + 1);
        }
        
        // 清理多余的空段落
        cleanEmptyParagraphsBetweenOrganizations(document);
        cleanExtraContentAfterLiujiangDutyInfo(document);
        
        log.info("✅ 组织机构内容插入完成");
    }

    /**
     * 递归处理组织机构树形节点
     * @param document Word文档
     * @param dept 当前部门
     * @param level 层级（0=根节点，1=一级子节点...）
     * @param index 当前层级的序号
     */
    private void processOrganizationTreeNode(XWPFDocument document, EmPrePlanDeptDTO dept, int level, int index) {
        if (dept == null) {
            return;
        }

        log.debug("🔄 处理节点: {} (层级: {}, 序号: {})", dept.getDeptName(), level, index);

        // 添加部门标题
        addDepartmentTitle(document, dept, level, index);

        // 添加组成人员信息
        addDepartmentPersonnel(document, dept, level);

        // 添加主要职责
        if (StringUtils.isNotEmpty(dept.getDeptJob())) {
            addDepartmentDuty(document, dept, level);
        }

        // 递归处理子部门
        if (dept.getChildren() != null && !dept.getChildren().isEmpty()) {
            log.debug("🌿 部门 '{}' 有 {} 个子部门", dept.getDeptName(), dept.getChildren().size());
            for (int i = 0; i < dept.getChildren().size(); i++) {
                EmPrePlanDeptDTO child = dept.getChildren().get(i);
                processOrganizationTreeNode(document, child, level + 1, i + 1);
            }
        }
    }

    /**
     * 添加部门标题
     */
    private void addDepartmentTitle(XWPFDocument document, EmPrePlanDeptDTO dept, int level, int index) {
        XWPFParagraph titleParagraph = document.createParagraph();
        titleParagraph.setStyle("2"); // 设置为标题2样式
        titleParagraph.setAlignment(ParagraphAlignment.LEFT);
        setFirstLineIndent(titleParagraph, 640); // 设置首行缩进2个字符

        XWPFRun titleRun = titleParagraph.createRun();
        
        // 根据层级设置不同的格式
        String titleText = generateDepartmentTitleText(dept, level, index);
        titleRun.setText(titleText);

        // 设置字体样式
        if (level == 0) {
            // 根节点（顶级机构）- 黑体三号字
            setParagraphSpacing(titleParagraph, 8, 3);
            titleRun.setFontFamily("黑体");
            titleRun.setFontSize(16); // 三号字体约16磅
            titleRun.setBold(true);
        } else {
            // 子机构 - 楷体_GB2312三号字
            setParagraphSpacing(titleParagraph, 4, 2);
            titleRun.setFontFamily("楷体_GB2312");
            titleRun.setFontSize(16); // 三号字体约16磅
            titleRun.setBold(true);
        }

        log.debug("📝 添加部门标题: {}", titleText);
    }

    /**
     * 生成部门标题文本
     */
    private String generateDepartmentTitleText(EmPrePlanDeptDTO dept, int level, int index) {
        switch (level) {
            case 0:
                // 根节点：（一）、（二）...
                return "（" + convertToChineseNumber(index) + "）" + dept.getDeptName();
            case 1:
                // 一级子节点：1、2、3...
                return index + "、" + dept.getDeptName();
            case 2:
                // 二级子节点：（1）、（2）...
                return "（" + index + "）" + dept.getDeptName();
            default:
                // 更深层级：①、②...或其他格式
                return index + "." + dept.getDeptName();
        }
    }

    /**
     * 添加部门人员信息
     */
    private void addDepartmentPersonnel(XWPFDocument document, EmPrePlanDeptDTO dept, int level) {
        // 总是显示"组成人员"标题
        XWPFParagraph personnelTitleParagraph = document.createParagraph();
        personnelTitleParagraph.setStyle("2"); // 设置为标题2样式
        personnelTitleParagraph.setAlignment(ParagraphAlignment.LEFT);
        setParagraphSpacing(personnelTitleParagraph, 3, 2);
        setFirstLineIndent(personnelTitleParagraph, 640);
        
        XWPFRun personnelTitleRun = personnelTitleParagraph.createRun();
        personnelTitleRun.setText("组成人员：");
        personnelTitleRun.setFontFamily("仿宋");
        personnelTitleRun.setFontSize(16);
        personnelTitleRun.setBold(true);

        // 添加具体人员信息
        addPersonnelDetails(document, dept);
    }

    /**
     * 判断是否应该显示人员部分（即使没有具体人员）
     */
    private boolean shouldShowPersonnelSection(EmPrePlanDeptDTO dept) {
        // 对于重要的机构，即使没有人员信息也要显示组成人员部分
        String deptName = dept.getDeptName();
        return deptName != null && (
            deptName.contains("指挥") || 
            deptName.contains("领导") || 
            deptName.contains("小组") ||
            deptName.contains("专家")
        );
    }

    /**
     * 添加人员详细信息
     */
    private void addPersonnelDetails(XWPFDocument document, EmPrePlanDeptDTO dept) {
        boolean hasContent = false;

        // 组长/指挥 - 只有有数据时才显示
        if (StringUtils.isNotEmpty(dept.getLeader())) {
            String leaderTitle = determineLeaderTitle(dept.getDeptName());
            addPersonnelTitle(document, leaderTitle);
            addPersonnelContent(document, dept.getLeader());
            hasContent = true;
        }

        // 副组长/副指挥 - 只有有数据时才显示
        if (StringUtils.isNotEmpty(dept.getLeaderAss())) {
            String assistantTitle = determineAssistantTitle(dept.getDeptName());
            addPersonnelTitle(document, assistantTitle);
            addPersonnelContent(document, dept.getLeaderAss());
            hasContent = true;
        }

        // 成员 - 只有有数据时才显示
        if (StringUtils.isNotEmpty(dept.getMember())) {
            addPersonnelTitle(document, "成员");
            addPersonnelContent(document, dept.getMember());
            hasContent = true;
        }

        // 专家 - 只有有数据时才显示
        if (StringUtils.isNotEmpty(dept.getPro())) {
            addPersonnelTitle(document, "专家");
            addPersonnelContent(document, dept.getPro());
            hasContent = true;
        }
        

        log.debug("👥 添加人员信息完成，实际显示条目数：{}", hasContent ? "有数据" : "无数据");
    }

    /**
     * 添加人员标题（在大纲中显示）
     */
    private void addPersonnelTitle(XWPFDocument document, String title) {
        XWPFParagraph titleParagraph = document.createParagraph();
        titleParagraph.setStyle("3"); // 设置为标题3样式，这样会在大纲中显示
        titleParagraph.setAlignment(ParagraphAlignment.LEFT);
        setParagraphSpacing(titleParagraph, 2, 1);
        setFirstLineIndent(titleParagraph, 640); // 设置首行缩进2个字符
        
        XWPFRun titleRun = titleParagraph.createRun();
        titleRun.setText(title + "：");
        titleRun.setFontFamily("仿宋");
        titleRun.setFontSize(16);
        titleRun.setBold(true);
    }

    /**
     * 添加人员内容
     */
    private void addPersonnelContent(XWPFDocument document, String content) {
        XWPFParagraph contentParagraph = document.createParagraph();
        contentParagraph.setAlignment(ParagraphAlignment.LEFT);
        setParagraphSpacing(contentParagraph, 1, 2);
        setFirstLineIndent(contentParagraph, 640); // 设置首行缩进2个字符
        
        XWPFRun contentRun = contentParagraph.createRun();
        contentRun.setText(content);
        contentRun.setFontFamily("方正仿宋_GB2312");
        contentRun.setFontSize(16);
        contentRun.setBold(false);
    }

    /**
     * 根据部门名称确定领导职务名称
     */
    private String determineLeaderTitle(String deptName) {
        if (deptName == null) return "组长";
        
        if (deptName.contains("指挥")) {
            return "总指挥";
        } else if (deptName.contains("领导")) {
            return "组长";
        } else if (deptName.contains("专家")) {
            return "组长";
        } else {
            return "组长";
        }
    }

    /**
     * 根据部门名称确定副职职务名称
     */
    private String determineAssistantTitle(String deptName) {
        if (deptName == null) return "副组长";
        
        if (deptName.contains("指挥")) {
            return "副总指挥";
        } else if (deptName.contains("领导")) {
            return "副组长";
        } else {
            return "副组长";
        }
    }

    /**
     * 添加部门职责信息
     */
    private void addDepartmentDuty(XWPFDocument document, EmPrePlanDeptDTO dept, int level) {
        // 添加"主要职责"标题
        XWPFParagraph dutyTitleParagraph = document.createParagraph();
        dutyTitleParagraph.setStyle("2"); // 设置为标题2样式
        dutyTitleParagraph.setAlignment(ParagraphAlignment.LEFT);
        setParagraphSpacing(dutyTitleParagraph, 3, 2);
        setFirstLineIndent(dutyTitleParagraph, 640);
        
        XWPFRun dutyTitleRun = dutyTitleParagraph.createRun();
        dutyTitleRun.setText("主要职责：");
        dutyTitleRun.setFontFamily("仿宋");
        dutyTitleRun.setFontSize(16);
        dutyTitleRun.setBold(true);

        // 添加职责内容
        XWPFParagraph dutyContentParagraph = document.createParagraph();
        dutyContentParagraph.setAlignment(ParagraphAlignment.LEFT);
        setParagraphSpacing(dutyContentParagraph, 2, 4);
        setFirstLineIndent(dutyContentParagraph, 640);

        XWPFRun dutyContentRun = dutyContentParagraph.createRun();
        dutyContentRun.setText(dept.getDeptJob());
        dutyContentRun.setFontFamily("仿宋");
        dutyContentRun.setFontSize(16);

        log.debug("📋 添加职责信息: {}", dept.getDeptJob().length() > 50 ? 
                 dept.getDeptJob().substring(0, 50) + "..." : dept.getDeptJob());
    }

    /**
     * 分析组织机构数据结构
     */
    private void analyzeOrganizationStructure(List<EmPrePlanDeptDTO> deptList) {
        log.info("=== 开始分析组织机构数据结构 ===");
        log.info("总部门数量: {}", deptList.size());

        // 统计各级别部门数量
        Map<Integer, Integer> levelCount = new HashMap<>();
        Map<String, Integer> parentCount = new HashMap<>();

        for (int i = 0; i < deptList.size(); i++) {
            EmPrePlanDeptDTO dept = deptList.get(i);
            // 统计层级
            int level = dept.getDeptLevel() != null ? dept.getDeptLevel() : 0;
            levelCount.put(level, levelCount.getOrDefault(level, 0) + 1);

            // 统计父级
            String parentId = dept.getParentId() != null ? dept.getParentId() : "null";
            parentCount.put(parentId, parentCount.getOrDefault(parentId, 0) + 1);

            log.debug("部门[{}]: {}, 层级: {}, 父级ID: {}, 序号: {}, 子部门数量: {}",
                      i, dept.getDeptName(), dept.getDeptLevel(), dept.getParentId(),
                      dept.getOrderNum(), dept.getChildren() != null ? dept.getChildren().size() : 0);
        }

        log.info("各层级部门统计: {}", levelCount);
        log.info("各父级部门统计: {}", parentCount);
        log.info("数据处理方式: 按原始顺序，不重新排序");
        log.info("间距规范: 主标题28磅前/6磅后, 部门标题8磅前/3磅后, 内容4磅前/2磅后");
        log.info("=== 组织机构数据结构分析完成 ===");
    }
    
    /**
     * 计算与"现将有关事项通知如下"的动态间距
     */
    private int calculateSpacingFromNotice(XWPFDocument document) {
        // 查找包含"现将有关事项通知如下"的段落
        XWPFParagraph noticeParagraph = null;
        int noticeIndex = -1;
        
        List<XWPFParagraph> paragraphs = document.getParagraphs();
        for (int i = 0; i < paragraphs.size(); i++) {
            XWPFParagraph paragraph = paragraphs.get(i);
        String text = paragraph.getText();
            if (text != null && text.contains("现将有关事项通知如下")) {
                noticeParagraph = paragraph;
                noticeIndex = i;
                log.info("找到'现将有关事项通知如下'在第{}个段落", i + 1);
                break;
            }
        }
        
        if (noticeParagraph != null) {
            // 设置段前28磅间距，适当的空间
            return 28; // 28磅间距，适中的空间
        } else {
            return 28; // 28磅间距
        }
    }

    /**
     * 添加分页符
     */
    private void addPageBreak(XWPFDocument document) {
        XWPFParagraph pageBreakParagraph = document.createParagraph();
        XWPFRun pageBreakRun = pageBreakParagraph.createRun();
        pageBreakRun.addBreak(BreakType.PAGE);
    }

    /**
     * 处理子部门内容（增强版）- 支持动态层级和数据结构
     */
    private int processChildDepartments(XWPFDocument document, List<EmPrePlanDeptDTO> children, int level) {
        if (children == null || children.isEmpty()) {
            return 0;
        }
        
        log.info("处理第{}级子部门，数量: {}", level, children.size());
        
        int paragraphCount = 0;
        
        // 动态排序子部门
        children.sort((a, b) -> {
            if (a.getOrderNum() != null && b.getOrderNum() != null) {
                return a.getOrderNum().compareTo(b.getOrderNum());
            } else if (a.getOrderNum() != null) {
                return -1;
            } else if (b.getOrderNum() != null) {
                return 1;
            } else {
                return a.getDeptName().compareTo(b.getDeptName());
            }
        });
        
        // 为子部门动态设置序号
        for (int i = 0; i < children.size(); i++) {
            EmPrePlanDeptDTO child = children.get(i);
            
            // 动态设置orderNum
            if (child.getOrderNum() == null) {
                child.setOrderNum(i + 1);
                log.debug("为部门'{}'设置序号: {}", child.getDeptName(), i + 1);
            }
            
            // 根据层级和是否有子部门决定编号格式
            String prefix = generateDepartmentPrefix(child, level, i + 1);
            
            // 部门名称 - 设置为标题2样式
            XWPFParagraph deptParagraph = document.createParagraph();
            deptParagraph.setStyle("2"); // 设置为标题2
            deptParagraph.setAlignment(ParagraphAlignment.LEFT); // 左对齐
            setParagraphSpacing(deptParagraph, 3, 1); // 段前3磅，段后1磅 - 进一步减小间距
            setFirstLineIndent(deptParagraph, 640); // 设置首行缩进2个字符（640 twips）
            XWPFRun deptRun = deptParagraph.createRun();
            
            // 与"各相关单位"对齐
            deptRun.setText(prefix + child.getDeptName());
            deptRun.setFontFamily("楷体_GB2312");
            deptRun.setFontSize(16); // 三号字体约16磅
            deptRun.setBold(true);
            paragraphCount++;
            
            log.debug("添加部门: {} (层级: {}, 前缀: {})", child.getDeptName(), level, prefix);

            // 添加组成人员信息（即使为空也显示标题）
            paragraphCount += addPersonnelInfo(document, child, level);

            // 添加主要职责
            if (StringUtils.isNotEmpty(child.getDeptJob())) {
                paragraphCount += addDutyInfo(document, child, level);
            }

            // 递归处理子部门
            if (child.getChildren() != null && !child.getChildren().isEmpty()) {
                log.debug("部门'{}'有{}个子部门，递归处理", child.getDeptName(), child.getChildren().size());
                paragraphCount += processChildDepartments(document, child.getChildren(), level + 1);
            }
        }
        
        return paragraphCount;
    }
    
    /**
     * 生成部门编号前缀
     */
    private String generateDepartmentPrefix(EmPrePlanDeptDTO dept, int level, int index) {
        boolean hasChildren = dept.getChildren() != null && !dept.getChildren().isEmpty();
        
        switch (level) {
            case 1: // 第一级子部门
                return hasChildren ? String.valueOf(index) : index + "、";
            case 2: // 第二级子部门  
                // 对于第二级子部门，需要找到父部门的编号
                return String.format("%d.%d ", getParentIndex(dept), index);
            case 3: // 第三级子部门
                return String.format("（%d）", index);
            default: // 更深层级
                return String.format("%d.", index);
        }
    }
    
    /**
     * 获取父部门的编号（简化处理）
     */
    private int getParentIndex(EmPrePlanDeptDTO dept) {
        // 如果有orderNum就使用，否则默认为1
        return dept.getOrderNum() != null ? dept.getOrderNum() : 1;
    }
    
    /**
     * 生成根据层级的缩进
     */
    private String generateIndent(int level) {
        StringBuilder indent = new StringBuilder();
        for (int i = 0; i < level; i++) {
            indent.append("    "); // 每层级4个空格缩进
        }
        return indent.toString();
    }
    
    /**
     * 检查是否有人员信息
     */
    private boolean hasPersonnelInfo(EmPrePlanDeptDTO dept) {
        return StringUtils.isNotEmpty(dept.getLeader()) ||
               StringUtils.isNotEmpty(dept.getLeaderAss()) ||
               StringUtils.isNotEmpty(dept.getMember()) ||
               StringUtils.isNotEmpty(dept.getPro());
    }
    
    /**
     * 添加人员信息
     */
    private int addPersonnelInfo(XWPFDocument document, EmPrePlanDeptDTO dept, int level) {
        int count = 0;
        
        // 组成人员标题 - 与"各相关单位"对齐
        XWPFParagraph memberTitleParagraph = document.createParagraph();
        memberTitleParagraph.setStyle("2");
        memberTitleParagraph.setAlignment(ParagraphAlignment.LEFT);
        setParagraphSpacing(memberTitleParagraph, 8, 3); // 统一间距：段前8磅，段后3磅
        setFirstLineIndent(memberTitleParagraph, 640); // 设置首行缩进2个字符（640 twips）
        XWPFRun memberTitleRun = memberTitleParagraph.createRun();
        memberTitleRun.setText("（1）组成人员");
        memberTitleRun.setFontFamily("仿宋");
        memberTitleRun.setFontSize(16); // 三号字体约16磅
        memberTitleRun.setBold(true);
        count++;

        // 组长
        if (StringUtils.isNotEmpty(dept.getLeader())) {
            count += addPersonnelLine(document, "组  长：", dept.getLeader());
        } else {
            count += addPersonnelLine(document, "组  长：", "待定");
        }

        // 副组长
        if (StringUtils.isNotEmpty(dept.getLeaderAss())) {
            count += addPersonnelLine(document, "副组长：", dept.getLeaderAss());
        } else {
            count += addPersonnelLine(document, "副组长：", "待定");
        }

        // 成员
        if (StringUtils.isNotEmpty(dept.getMember())) {
            count += addPersonnelLine(document, "成  员：", dept.getMember());
        } else {
            count += addPersonnelLine(document, "成  员：", "待定");
        }
        
        // 专家（只有在有值的时候才显示）
        if (StringUtils.isNotEmpty(dept.getPro())) {
            count += addPersonnelLine(document, "专  家：", dept.getPro());
        }
        
        return count;
    }
    
    /**
     * 添加人员信息行
     */
    private int addPersonnelLine(XWPFDocument document, String label, String content) {
        XWPFParagraph paragraph = document.createParagraph();
        paragraph.setAlignment(ParagraphAlignment.LEFT);
        setParagraphSpacing(paragraph, 4, 2); // 统一内容间距：段前4磅，段后2磅
        setFirstLineIndent(paragraph, 640); // 设置首行缩进2个字符（640 twips）
        
        // 标签（加粗）
        XWPFRun labelRun = paragraph.createRun();
        labelRun.setText(label);
        labelRun.setFontFamily("仿宋");
        labelRun.setFontSize(16); // 三号字体约16磅
        labelRun.setBold(true);
        
        // 内容（应用_Style13样式，使用方正仿宋_GB2312字体，三号，不加粗）
        XWPFRun contentRun = paragraph.createRun();
        contentRun.setText(content);
        contentRun.setFontFamily("方正仿宋_GB2312"); // 改为方正仿宋_GB2312
        contentRun.setFontSize(16); // 三号字体约16磅
        contentRun.setBold(false); // 不加粗
        
        // 为内容Run应用_Style13样式
        applyStyle13ToRun(contentRun);
        
        return 1;
    }
    
    /**
     * 添加职责信息
     */
    private int addDutyInfo(XWPFDocument document, EmPrePlanDeptDTO dept, int level) {
        int count = 0;
        
        log.info("=== 开始添加主要职责信息 ===");
        log.info("部门名称: {}", dept.getDeptName());
        log.info("部门层级: {}", level);
        log.info("职责内容: {}", dept.getDeptJob() != null ? dept.getDeptJob().substring(0, Math.min(50, dept.getDeptJob().length())) + "..." : "无");
        
        // 主要职责标题 - 与"各相关单位"对齐
        XWPFParagraph dutyTitleParagraph = document.createParagraph();
        dutyTitleParagraph.setStyle("2");
        dutyTitleParagraph.setAlignment(ParagraphAlignment.LEFT);
        
        // 根据部门类型动态调整主要职责标题的间距
        int titleSpacing = calculateDutyTitleSpacing(dept);
        log.info("主要职责标题间距: 段前{}磅, 段后3磅", titleSpacing);
        setParagraphSpacing(dutyTitleParagraph, titleSpacing, 3); // 动态段前间距，段后3磅
        setFirstLineIndent(dutyTitleParagraph, 640); // 设置首行缩进2个字符（640 twips）
        XWPFRun dutyTitleRun = dutyTitleParagraph.createRun();
        dutyTitleRun.setText("（2）主要职责");
        dutyTitleRun.setFontFamily("仿宋");
        dutyTitleRun.setFontSize(16); // 三号字体约16磅
        dutyTitleRun.setBold(true);
        count++;

        // 职责内容（应用方正仿宋_GB2312字体和_Style13样式）- 与"各相关单位"对齐
        XWPFParagraph dutyParagraph = document.createParagraph();
        dutyParagraph.setAlignment(ParagraphAlignment.LEFT);
        
        // 根据部门类型动态调整职责内容的间距
        int contentSpacing = calculateDutyContentSpacing(dept);
        int afterSpacing = calculateDutyContentAfterSpacing(dept); // 动态计算段后间距
        log.info("主要职责内容间距: 段前{}磅, 段后{}磅", contentSpacing, afterSpacing);
        log.info(">>> 关键信息: 部门'{}' 的主要职责内容段后间距为{}磅", dept.getDeptName(), afterSpacing);
        setParagraphSpacing(dutyParagraph, contentSpacing, afterSpacing); // 动态段前间距和段后间距
        setFirstLineIndent(dutyParagraph, 640); // 设置首行缩进2个字符（640 twips）
        XWPFRun dutyRun = dutyParagraph.createRun();
        dutyRun.setText(dept.getDeptJob());
        dutyRun.setFontFamily("方正仿宋_GB2312"); // 改为方正仿宋_GB2312
        dutyRun.setFontSize(16); // 三号字体约16磅
        dutyRun.setBold(false); // 不加粗
        
        // 为职责内容应用_Style13样式
        applyStyle13ToRun(dutyRun);
        
        count++;
        
        log.info("主要职责信息添加完成，总段落数: {}", count);
        log.info("=== 主要职责信息添加结束 ===");
        
        return count;
    }

    /**
     * 设置段落间距
     * @param paragraph 段落
     * @param beforePoints 段前间距（磅）
     * @param afterPoints 段后间距（磅）
     */
    private void setParagraphSpacing(XWPFParagraph paragraph, int beforePoints, int afterPoints) {
                    CTP ctp = paragraph.getCTP();
                    CTPPr ppr = ctp.isSetPPr() ? ctp.getPPr() : ctp.addNewPPr();
                    CTSpacing spacing = ppr.isSetSpacing() ? ppr.getSpacing() : ppr.addNewSpacing();

        // 设置段前间距（磅转换为twips：1磅=20twips）
        spacing.setBefore(BigInteger.valueOf(beforePoints * 20));
        // 设置段后间距
        spacing.setAfter(BigInteger.valueOf(afterPoints * 20));
        
        log.info("✅ 实际设置间距: 段前{}磅({}twips), 段后{}磅({}twips)", 
                  beforePoints, beforePoints * 20, afterPoints, afterPoints * 20);
    }

    /**
     * 设置段落行距
     * @param paragraph 段落
     * @param lineSpacing 行距倍数（1.0为单倍行距，0.0为最小行距）
     */
    private void setLineSpacing(XWPFParagraph paragraph, double lineSpacing) {
        log.info("⚡ 设置行距: {}倍", lineSpacing);
        
        try {
            CTP ctp = paragraph.getCTP();
            CTPPr ppr = ctp.isSetPPr() ? ctp.getPPr() : ctp.addNewPPr();
            CTSpacing spacing = ppr.isSetSpacing() ? ppr.getSpacing() : ppr.addNewSpacing();
            
            if (lineSpacing == 0.0) {
                // 特殊处理：0倍行距意味着最小行距（类似用户手动设置为0的效果）
                spacing.setLineRule(org.openxmlformats.schemas.wordprocessingml.x2006.main.STLineSpacingRule.EXACT);
                spacing.setLine(BigInteger.valueOf(240)); // 240 twips = 12磅，确保文字可见
                log.info("✅ 设置为固定行距（EXACT规则，值为240 twips = 12磅），实现紧凑但可见的效果");
            } else {
                // 设置行距规则为多倍行距
                spacing.setLineRule(org.openxmlformats.schemas.wordprocessingml.x2006.main.STLineSpacingRule.AUTO);
                // 设置行距值 (乘以240，因为Word内部使用的单位)
                spacing.setLine(BigInteger.valueOf((long)(lineSpacing * 240)));
                log.info("✅ 行距设置完成: {}倍行距（AUTO规则）", lineSpacing);
            }
            
        } catch (Exception e) {
            log.error("❌ 设置行距失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 将DTO转换为VO
     *
     * @param dto DTO对象
     * @return VO对象
     */
    private EmergencyEventVO convertToVO(EmergencyEventDTO dto) {
        EmergencyEventVO vo = new EmergencyEventVO();
        BeanUtils.copyProperties(dto, vo);

        // TODO: 设置字典名称、用户名称等
        // 可以在这里调用字典服务和用户服务来获取名称

        return vo;
    }

    /**
     * 获取当前登录用户ID
     *
     * @return 当前用户ID
     */
    private String getCurrentUserId() {
        try {
            // 使用SecurityUtils获取当前登录用户ID
            Long userId = SecurityUtils.getUserId();
            return userId != null ? userId.toString() : null;
        } catch (Exception e) {
            // 如果获取失败，返回null或默认值
            return null;
        }
    }

    /**
     * 创建应急事件告警
     *
     * @param createDTO 应急事件创建DTO（包含完整数据）
     * @param eventDTO  应急事件DTO（主表数据）
     */
    private void createEmergencyEventAlarm(EmergencyEventCreateDTO createDTO, EmergencyEventDTO eventDTO) {
        try {
            AlarmInfoDTO alarmInfo = new AlarmInfoDTO();

            // 设置告警标题：xx行政辖区发生xx事件
            String alarmTitle = buildAlarmTitle(createDTO);
            alarmInfo.setAlarmTitle(alarmTitle);

            alarmInfo.setAlarmType("2"); // 应急类型
            alarmInfo.setAlarmSubtype("2"); // 新事件子类型
            alarmInfo.setAlarmLevel(mapEventLevelToAlarmLevel(eventDTO.getEventLevel()));

            // 设置详细的告警内容
            String alarmContent = buildAlarmContent(createDTO);
            alarmInfo.setAlarmContent(alarmContent);

            alarmInfo.setSourceId(eventDTO.getEventId());
            alarmInfo.setSourceType("event");

            // 设置行政辖区信息
            alarmInfo.setAdministrativeAreaId(eventDTO.getAdministrativeAreaId());
            alarmInfo.setAdministrativeArea(eventDTO.getAdministrativeArea());

            // 通过填报人ID查询用户信息获取部门信息
            String reporterId = eventDTO.getReporterId();
            if (reporterId != null) {
                try {
                    Long userId = Long.parseLong(reporterId);
                    SysUser reporter = userService.selectUserById(userId);
                    if (reporter != null && reporter.getDept() != null) {
                        alarmInfo.setOrgId(reporter.getDeptId().toString());
                        alarmInfo.setOrgName(reporter.getDept().getDeptName());
                    } else {
                        // 如果查询不到部门信息，使用默认值
                        alarmInfo.setOrgId(reporterId);
                        alarmInfo.setOrgName("未知部门");
                    }
                } catch (NumberFormatException e) {
                    // 如果填报人ID不是数字，使用默认值
                    alarmInfo.setOrgId(reporterId);
                    alarmInfo.setOrgName("未知部门");
                }
            } else {
                // 如果没有填报人ID，使用当前用户的部门信息
                Long currentUserId = SecurityUtils.getUserId();
                Long currentDeptId = SecurityUtils.getDeptId();
                alarmInfo.setOrgId(currentDeptId != null ? currentDeptId.toString() : "");
                alarmInfo.setOrgName("当前用户部门");
            }


            // 调用告警服务创建告警
            alarmService.insertAlarmInfo(alarmInfo);
        } catch (Exception e) {
            // 告警创建失败不影响主业务流程，只记录日志
            log.error("创建应急事件告警失败: " + e.getMessage(), e);
        }
    }

    /**
     * 构建告警标题
     *
     * @param createDTO 应急事件创建DTO
     * @return 告警标题
     */
    private String buildAlarmTitle(EmergencyEventCreateDTO createDTO) {
        String administrativeArea = createDTO.getAdministrativeArea();
        String eventTypeName = getEventTypeName(createDTO.getEventType());

        // 格式：xx行政辖区发生xx事件
        return String.format("%s发生%s",
                administrativeArea != null ? administrativeArea : "未知区域",
                eventTypeName != null ? eventTypeName : "突发事件");
    }

    /**
     * 构建告警内容
     *
     * @param createDTO 应急事件创建DTO
     * @return 告警内容
     */
    private String buildAlarmContent(EmergencyEventCreateDTO createDTO) {
        StringBuilder content = new StringBuilder();

        // 发生时间
        if (createDTO.getOccurTime() != null) {
            content.append(formatTimestampToChinese(createDTO.getOccurTime()));
        }

        // 事故地址
        if (createDTO.getDetailedAddress() != null) {
            content.append("，在").append(createDTO.getDetailedAddress());
        }

        // 事件类型和事故类型
        String eventTypeName = getEventTypeName(createDTO.getEventType());
        if (eventTypeName != null) {
            content.append("发生一起").append(eventTypeName);
        }

        // 事件描述（原因）
        if (createDTO.getEventDescription() != null) {
            content.append("，").append(createDTO.getEventDescription());
        }

        // 根据事件类型添加扩展信息
        if ("1".equals(createDTO.getEventType())) { // 道路交通事故
            appendRoadTrafficInfoText(content, createDTO);
        } else if ("2".equals(createDTO.getEventType())) { // 水路交通事故
            appendWaterwayTrafficInfoText(content, createDTO);
        }

        content.append("。");
        return content.toString();
    }

    /**
     * 将事件级别映射为告警级别
     *
     * @param eventLevel 事件级别
     * @return 告警级别
     */
    private String mapEventLevelToAlarmLevel(String eventLevel) {
        if (eventLevel == null) {
            return "1"; // 默认一般级别
        }

        switch (eventLevel) {
            case "1": // 事件Ⅰ级(特别重大)
                return "4"; // 告警严重
            case "2": // 事件Ⅱ级(重大)
                return "3"; // 告警紧急
            case "3": // 事件Ⅲ级(较大)
                return "2"; // 告警重要
            case "4": // 事件Ⅳ级(一般)
                return "1"; // 告警一般
            default:
                return "1"; // 默认一般级别
        }
    }

    /**
     * 添加道路交通事故扩展信息（文本格式）
     *
     * @param content   内容构建器
     * @param createDTO 应急事件创建DTO
     */
    private void appendRoadTrafficInfoText(StringBuilder content, EmergencyEventCreateDTO createDTO) {
        // 是否影响通行
        if (createDTO.getTrafficAffected() != null) {
            if ("Y".equals(createDTO.getTrafficAffected())) {
                content.append("。该事故影响通行");

                // 预计恢复时间
                if (createDTO.getEstimatedRecoveryTime() != null) {
                    content.append("，预计于").append(formatTimestampToChinese(createDTO.getEstimatedRecoveryTime())).append("恢复通行");
                }
            } else {
                content.append("。该事故不影响通行");
            }
        }

        // 人员伤亡情况
        if (createDTO.getRoadCasualtySituation() != null) {
            content.append("，目前").append(createDTO.getRoadCasualtySituation());
        }
    }

    /**
     * 添加水路交通事故扩展信息（文本格式）
     *
     * @param content   内容构建器
     * @param createDTO 应急事件创建DTO
     */
    private void appendWaterwayTrafficInfoText(StringBuilder content, EmergencyEventCreateDTO createDTO) {
        // 船舶信息
        if (createDTO.getShipName() != null) {
            content.append("，涉事船舶为").append(createDTO.getShipName());

            // 船舶吨位
            if (createDTO.getShipTonnage() != null) {
                content.append("（").append(createDTO.getShipTonnage()).append("吨）");
            }
        }

        // 货物信息
        if (createDTO.getCargoInfo() != null) {
            content.append("，载有").append(createDTO.getCargoInfo());
        }

        // 人员伤亡情况
        if (createDTO.getWaterwayCasualtySituation() != null) {
            content.append("，目前").append(createDTO.getWaterwayCasualtySituation());
        }

        // 环境影响
        if (createDTO.getEnvironmentalImpact() != null) {
            content.append("，").append(createDTO.getEnvironmentalImpact());
        }
    }

    /**
     * 格式化时间戳
     *
     * @param timestamp 时间戳（秒）
     * @return 格式化后的时间字符串
     */
    private String formatTimestamp(Long timestamp) {
        if (timestamp == null) {
            return "";
        }
        try {
            java.util.Date date = new java.util.Date(timestamp * 1000);
            java.text.SimpleDateFormat sdf = new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            return sdf.format(date);
        } catch (Exception e) {
            return timestamp.toString();
        }
    }

    /**
     * 格式化时间戳为中文年月日时分秒格式
     *
     * @param timestamp 时间戳（秒）
     * @return 格式化后的中文时间字符串（如：2025年06月01日17时25分30秒）
     */
    private String formatTimestampToChinese(Long timestamp) {
        if (timestamp == null) {
            return "";
        }
        try {
            java.util.Date date = new java.util.Date(timestamp * 1000);
            java.text.SimpleDateFormat sdf = new java.text.SimpleDateFormat("yyyy年MM月dd日HH时mm分ss秒");
            return sdf.format(date);
        } catch (Exception e) {
            return formatTimestamp(timestamp);
        }
    }

    /**
     * 获取事件类型名称
     * TODO: 这里应该调用字典服务获取真实的字典值，暂时使用硬编码
     *
     * @param eventType 事件类型值
     * @return 事件类型名称
     */
    private String getEventTypeName(String eventType) {
        if (eventType == null) {
            return null;
        }
        switch (eventType) {
            case "1":
                return "道路交通事故";
            case "2":
                return "水路交通事故";
            default:
                return "突发事件";
        }
    }

    /**
     * 获取事故类型名称
     * TODO: 这里应该调用字典服务获取真实的字典值，暂时使用硬编码
     *
     * @param accidentType 事故类型值
     * @return 事故类型名称
     */
    private String getAccidentTypeName(String accidentType) {
        if (accidentType == null) {
            return null;
        }
        // 这里需要根据实际的字典数据进行映射
        // 暂时返回原值，后续可以调用字典服务
        return accidentType;
    }

    /**
     * 获取船舶类型名称
     * TODO: 这里应该调用字典服务获取真实的字典值，暂时使用硬编码
     *
     * @param shipType 船舶类型值
     * @return 船舶类型名称
     */
    private String getShipTypeName(String shipType) {
        if (shipType == null) {
            return null;
        }
        // 这里需要根据实际的字典数据进行映射
        // 暂时返回原值，后续可以调用字典服务
        return shipType;
    }

    public static void main(String[] args) {
        String token = getYkToken();
        System.out.println(token);
        NewEmerEventSmsDTO dto = new NewEmerEventSmsDTO();
        dto.setDate("2025年06月03日11时22分");
        dto.setLocation("G75兰海高速上行K2012+300");
        dto.setContent("发生一辆小车抛锚");
        smsSend(token, dto);
    }

    private static void smsSend(String token, NewEmerEventSmsDTO dto) {
        // 测试短信发送
        smsSend(token, "18176275992", "测试短信内容");
    }

    /**
     * 发送短信通知上报人
     *
     * @param createDTO 应急事件创建DTO
     * @param eventDTO  应急事件DTO
     */
    private void sendSmsToSubmitter(EmergencyEventCreateDTO createDTO, EmergencyEventDTO eventDTO) {
        try {
            // 获取上报人手机号
            String submitterMobile = getSubmitterMobile(eventDTO.getSubmitterId());
            if (submitterMobile == null || submitterMobile.isEmpty()) {
                log.warn("上报人手机号为空，无法发送短信通知，上报人ID: {}", eventDTO.getSubmitterId());
                return;
            }
            // 构建短信内容
            String smsContent = buildSmsContent(createDTO);

            // 获取token并发送短信
            String token = getYkToken();
            if (token != null) {
                smsSend(token, submitterMobile, smsContent);
                log.info("短信发送成功，手机号: {}, 内容: {}", submitterMobile, smsContent);
            } else {
                log.error("获取短信token失败，无法发送短信");
            }
        } catch (Exception e) {
            // 短信发送失败不影响主业务流程，只记录日志
            log.error("发送短信通知失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取上报人手机号
     *
     * @param submitterId 上报人ID
     * @return 手机号
     */
    private String getSubmitterMobile(String submitterId) {
        if (submitterId == null || submitterId.isEmpty()) {
            return null;
        }

        try {
            Long userId = Long.parseLong(submitterId);
            SysUser submitter = userService.selectUserById(userId);
            return submitter != null ? submitter.getPhonenumber() : null;
        } catch (NumberFormatException e) {
            log.error("上报人ID格式错误: {}", submitterId);
            return null;
        }
    }

    /**
     * 构建短信内容（JSON格式）
     *
     * @param createDTO 应急事件创建DTO
     * @return 短信内容（JSON格式）
     */
    private String buildSmsContent(EmergencyEventCreateDTO createDTO) {
        // 构建date字段（格式：2025年06月03日11时22分30秒）
        String dateStr = "";
        if (createDTO.getOccurTime() != null) {
            dateStr = formatTimestampToChinese(createDTO.getOccurTime());
        }

        // 构建location字段
        String locationStr = createDTO.getDetailedAddress() != null ? createDTO.getDetailedAddress() : "";

        // 构建content字段（参考告警内容格式）
        StringBuilder contentBuilder = new StringBuilder();

        // 事件类型
        String eventTypeName = getEventTypeName(createDTO.getEventType());
        if (eventTypeName != null) {
            contentBuilder.append("发生一起").append(eventTypeName);
        }

        // 事件描述
        if (createDTO.getEventDescription() != null && !createDTO.getEventDescription().isEmpty()) {
            contentBuilder.append("，").append(createDTO.getEventDescription());
        }

        // 根据事件类型添加扩展信息
        if ("1".equals(createDTO.getEventType())) { // 道路交通事故
            appendRoadTrafficSmsInfo(contentBuilder, createDTO);
        } else if ("2".equals(createDTO.getEventType())) { // 水路交通事故
            appendWaterwayTrafficSmsInfo(contentBuilder, createDTO);
        }

        // 构建JSON格式的内容
        return String.format("{\"date\":\"%s\",\"location\":\"%s\",\"content\":\"%s\"}",
                dateStr, locationStr, contentBuilder.toString());
    }

    /**
     * 添加道路交通事故扩展信息（短信格式）
     *
     * @param content   内容构建器
     * @param createDTO 应急事件创建DTO
     */
    private void appendRoadTrafficSmsInfo(StringBuilder content, EmergencyEventCreateDTO createDTO) {
        // 是否影响通行
        if (createDTO.getTrafficAffected() != null) {
            if ("Y".equals(createDTO.getTrafficAffected())) {
                content.append("。该事故影响通行");

                // 预计恢复时间
                if (createDTO.getEstimatedRecoveryTime() != null) {
                    String recoveryTimeStr = formatTimestampToChinese(createDTO.getEstimatedRecoveryTime());
                    content.append("，预计于").append(recoveryTimeStr).append("恢复通行");
                }
            } else {
                content.append("。该事故不影响通行");
            }
        }

        // 人员伤亡情况
        if (createDTO.getRoadCasualtySituation() != null && !createDTO.getRoadCasualtySituation().isEmpty()) {
            content.append("，目前").append(createDTO.getRoadCasualtySituation());
        }
    }

    /**
     * 添加水路交通事故扩展信息（短信格式）
     *
     * @param content   内容构建器
     * @param createDTO 应急事件创建DTO
     */
    private void appendWaterwayTrafficSmsInfo(StringBuilder content, EmergencyEventCreateDTO createDTO) {
        // 船舶信息
        if (createDTO.getShipName() != null && !createDTO.getShipName().isEmpty()) {
            content.append("，涉事船舶为").append(createDTO.getShipName());

            // 船舶吨位
            if (createDTO.getShipTonnage() != null) {
                content.append("（").append(createDTO.getShipTonnage()).append("吨）");
            }
        }

        // 货物信息
        if (createDTO.getCargoInfo() != null && !createDTO.getCargoInfo().isEmpty()) {
            content.append("，载有").append(createDTO.getCargoInfo());
        }

        // 人员伤亡情况
        if (createDTO.getWaterwayCasualtySituation() != null && !createDTO.getWaterwayCasualtySituation().isEmpty()) {
            content.append("，目前").append(createDTO.getWaterwayCasualtySituation());
        }

        // 环境影响
        if (createDTO.getEnvironmentalImpact() != null && !createDTO.getEnvironmentalImpact().isEmpty()) {
            content.append("，").append(createDTO.getEnvironmentalImpact());
        }
    }

    private static void smsSend(String token, String mobile, String content) {
        String url = "https://yktest.itsgx.cn:18763/sykpt/its-api/smsSend";
        Map<String, String> head = new HashMap<>();
        head.put("AuthorizationType", "other");
        head.put("Authorization", token);
        Map<String, String> body = new HashMap<>();
        body.put("mobile", mobile);
        body.put("content", content);
        body.put("signName", "智慧高速云控平台");
        body.put("templateKey","NEW_EMER_EVENT");
        String response = HttpClientUtils.postWithBody(10000, url, head, body);
        log.info("短信发送响应: {}", response);
    }

    private static String getYkToken() {
        String url = "https://yktest.itsgx.cn:18763/sykpt/its-api/login/otherGetToken";
        Map<String, String> body = new HashMap<>();
        body.put("appId", "fb9686ae-8706-46ce-926d-23ddfbc010e9");
        body.put("appSecret", "WTKkpkH*wAyFCKgbbfNz$sMnbuqd#Wj#");
        String response = HttpClientUtils.postWithBody(10000, url, null, body);
        YkTokenVO vo = new Gson().fromJson(response, YkTokenVO.class);
        if (vo.getCode() == 1) {
            return vo.getToken();
        }
        return null;
    }

    /**
     * 导出通知模板替换数据
     */
    private Map<String, String> prepareTemplateData(EmergencyEventDetailVO vo) {
        Map<String, String> data = new HashMap<>();

        // 设置基础数据
        String occurTimeValue = formatTimestampToChinese(vo.getOccurTime());
        data.put("${occurTime}", occurTimeValue);
        log.info("设置 ${occurTime}: {}", occurTimeValue);

        String noticeContent = buildNoticeContent(vo);
        data.put("${eventDesc}", noticeContent);
        log.info("设置 ${eventDesc}: {}", noticeContent);

        String eventTitle = vo.getEventTitle();
        data.put("${eventTitle}", eventTitle);
        log.info("设置 ${eventTitle}: {}", eventTitle);

        //获取当前的用户
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if (ObjectUtil.isNotNull(loginUser)) {
            SysUser currentUser = loginUser.getUser();
            if (StringUtils.isNotNull(currentUser) && StringUtils.isNotNull(currentUser.getDept())) {
                data.put("${unitName}", currentUser.getDept().getDeptName());
                data.put("${decision}", currentUser.getDept().getDeptName());
            }
        }
        String time = DateUtil.format(DateUtil.date(), "yyyy年MM月dd日");
        data.put("${time}", time);

        // 读取jobAsk配置信息
        String jobAsk = sysConfigService.selectConfigByKey("emNoticeJobAsk");
        data.put("${jobAsk}", StringUtils.isNotEmpty(jobAsk) ? jobAsk : "");
        log.info("设置 ${jobAsk}: {}", jobAsk);
        
        // 注意：organization占位符现在在downloadNotice方法中直接处理，不需要在这里生成文本

        return data;
    }

    /**
     * 使用模板样式构建组织机构文本
     */
    private void buildOrgTextWithStyle(List<EmPrePlanDeptDTO> deptList, StringBuilder text) {
        if (deptList == null || deptList.isEmpty()) {
            return;
        }
        
        int topLevelCount = 1;
        for (EmPrePlanDeptDTO dept : deptList) {
            // 根据层级添加不同的样式
            if (dept.getDeptLevel() == 0) {
                // 顶级机构样式
                text.append("（").append(convertToChineseNumber(topLevelCount++)).append("）")
                    .append(dept.getDeptName()).append("\n");
            } else {
                // 下级机构样式
                String prefix;
                if (dept.getDeptLevel() == 1) {
                    // 一级机构使用阿拉伯数字
                    prefix = String.format("%d、", dept.getOrderNum() != null ? dept.getOrderNum() : 1);
                } else {
                    // 二级机构使用阿拉伯数字加括号
                    prefix = String.format("（%d）", dept.getOrderNum() != null ? dept.getOrderNum() : 1);
                }

                text.append("    ").append(prefix).append(dept.getDeptName()).append("\n");

                // 添加组成人员和主要职责
                if (StringUtils.isNotEmpty(dept.getLeader()) ||
                    StringUtils.isNotEmpty(dept.getLeaderAss()) ||
                    StringUtils.isNotEmpty(dept.getMember())) {

                    text.append("    （1）组成人员\n");
                    
                    if (StringUtils.isNotEmpty(dept.getLeader())) {
                        text.append("        组  长：").append(dept.getLeader()).append("\n");
                    }
                    if (StringUtils.isNotEmpty(dept.getLeaderAss())) {
                        text.append("        副组长：").append(dept.getLeaderAss()).append("\n");
                    }
                    if (StringUtils.isNotEmpty(dept.getMember())) {
                        text.append("        成  员：").append(dept.getMember()).append("\n");
                    }
                }

                // 添加主要职责
                if (StringUtils.isNotEmpty(dept.getDeptJob())) {
                    text.append("    （2）主要职责\n");
                    text.append("        ").append(dept.getDeptJob()).append("\n");
                }

                text.append("\n");
            }

            // 递归处理子部门
            if (dept.getChildren() != null && !dept.getChildren().isEmpty()) {
                buildOrgTextWithStyle(dept.getChildren(), text);
            }
        }
    }

    /**
     * 将数字转换为中文数字
     */
    private String convertToChineseNumber(int num) {
        String[] chineseNumbers = {"零", "一", "二", "三", "四", "五", "六", "七", "八", "九", "十"};
        if (num <= 10) {
            return chineseNumbers[num];
        } else if (num < 20) {
            return "十" + (num % 10 == 0 ? "" : chineseNumbers[num % 10]);
        } else {
            return chineseNumbers[num / 10] + "十" + (num % 10 == 0 ? "" : chineseNumbers[num % 10]);
        }
    }

    /**
     * 构建通知突发事件描述
     *
     * @param vo
     * @return 突发事件描述
     */
    private String buildNoticeContent(EmergencyEventDetailVO vo) {
        StringBuilder content = new StringBuilder();
        //事故地址
        if (vo.getDetailedAddress() != null) {
            content.append("在").append(vo.getDetailedAddress());
        }
        //事件类型和事故类型
        String eventTypeName = getEventTypeName(vo.getEventType());
        if (eventTypeName != null) {
            content.append("发生一起").append(eventTypeName);
        }
        //事件描述（原因）
        if (vo.getEventDescription() != null) {
            content.append("，").append(vo.getEventDescription());
        }
        //根据事件类型添加扩展信息
        if ("1".equals(vo.getEventType())) { //道路交通事故
            //是否影响通行
            if (vo.getTrafficAffected() != null) {
                if ("Y".equals(vo.getTrafficAffected())) {
                    content.append("。该事故影响通行");
                    // 预计恢复时间
                    if (vo.getEstimatedRecoveryTime() != null) {
                        content.append("，预计于").append(formatTimestampToChinese(vo.getEstimatedRecoveryTime())).append("恢复通行");
                    }
                } else {
                    content.append("。该事故不影响通行");
                }
            }
            //人员伤亡情况
            if (vo.getRoadCasualtySituation() != null) {
                content.append("，目前").append(vo.getRoadCasualtySituation());
            }
        } else if ("2".equals(vo.getEventType())) { //水路交通事故
            //船舶信息
            if (vo.getShipName() != null) {
                content.append("，涉事船舶为").append(vo.getShipName());
                //船舶吨位
                if (vo.getShipTonnage() != null) {
                    content.append("（").append(vo.getShipTonnage()).append("吨）");
                }
            }
            //货物信息
            if (vo.getCargoInfo() != null) {
                content.append("，载有").append(vo.getCargoInfo());
            }
            //人员伤亡情况
            if (vo.getWaterwayCasualtySituation() != null) {
                content.append("，目前").append(vo.getWaterwayCasualtySituation());
            }
            //环境影响
            if (vo.getEnvironmentalImpact() != null) {
                content.append("，").append(vo.getEnvironmentalImpact());
            }
        }
        return content.toString();
    }

    @Override
    @SneakyThrows
    public void exportEmergencyEventDecisionDocx(String eventId, HttpServletResponse response) {
        EmergencyEventDetailVO vo = this.selectEmergencyEventDetailByEventId(eventId);
        if (ObjectUtil.isNull(vo)) {
            throw new ServiceException("没有找到符合条件的应急事件");
        }
        // 遍历文档中的所有段落并替换文本
        try (
                InputStream resourceAsStream = getClass().getClassLoader().getResourceAsStream("templates/应急事件辅助决策建议模板.docx");
                BufferedInputStream bos = new BufferedInputStream(resourceAsStream);
                XWPFDocument document = new XWPFDocument(bos)
        ) {
            for (XWPFParagraph paragraph : document.getParagraphs()) {
                replaceInParagraph(paragraph, buildEventDecisionContent(vo));
            }
            // 响应修改后的文档
            String fileName = String.format("交通运输部门应对%s的辅助决策建议.docx", vo.getEventTitle());
            String encodedFileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8.name()).replaceAll("\\+", "%20");
            response.setContentType("application/vnd.openxmlformats-officedocument.wordprocessingml.document");
            response.setHeader(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename*=UTF-8''" + encodedFileName);
            document.write(response.getOutputStream());
        } catch (IOException ex) {
            log.error("交通运输部门应对{}的辅助决策建议文档导出异常: [{}]", vo.getEventTitle(), ex.getMessage(), ex);
            throw new ServiceException(String.format("交通运输部门应对%s的辅助决策建议文档导出异常", vo.getEventTitle()));
        }
    }

    @Override
    public Boolean sendMsg(String content, String mobile) {
        String token = getYkToken();
        smsSend(token,content,mobile);
        return true;
    }

    private Map buildEventDecisionContent(EmergencyEventDetailVO vo) {
        StringBuffer contentTemplate = new StringBuffer("根据{0}突发事件对交通运输系统造成的影响，建议交通运输部门立即启动{1}级交通应急响应。经研判，此次事件【");
        Map<String, String> textMap = new HashMap<>();
        if (ObjectUtil.isNotNull(vo.getEstimatedRecoveryTime())) {
            DateTimeFormatter formatter = DateTimeFormatter
                    .ofPattern("yyyy年MM月dd日 HH时mm分ss秒")
                    .withZone(ZoneId.systemDefault()); // 设置系统默认时区
            String estimatedRecoveryTime = formatter.format(Instant.ofEpochSecond(vo.getEstimatedRecoveryTime()));
            contentTemplate.append(String.format("影响通行，预计于%s恢复通行，", estimatedRecoveryTime));
        }
        //{0}-标题，{1}-事件等级， {2}-伤亡情况，{3}-事件等级
        contentTemplate.append("目前{2}】，严重阻碍应急救援和物资运输通行，符合{3}级响应启动条件。响应启动后，迅速激活交通运输应急指挥体系，建立 24 小时应急值班制度，确保信息畅通、指令及时传达。");
        String level = LevelEnum.getRomeDescByCode(Integer.valueOf(vo.getEventLevel()));
        String content = MessageFormat.format(contentTemplate.toString(), vo.getEventTitle(), level, vo.getRoadCasualtySituation(), level);
        textMap.put("${eventTitle}", vo.getEventTitle());
        textMap.put("${respondContent}", content);
        return textMap;
    }

    private static void replaceInParagraph(XWPFParagraph paragraph, Map<String, String> replacements) {
        Logger logger = LoggerFactory.getLogger(EmergencyEventServiceImpl.class);
        try {
            if (paragraph == null) {
                logger.warn("段落为空，跳过替换");
                return;
            }
            
            if (replacements == null || replacements.isEmpty()) {
                logger.warn("替换映射为空，跳过替换");
                return;
            }
            
            String text = paragraph.getText();
            if (text == null || text.isEmpty()) {
                logger.debug("段落文本为空，跳过替换");
                return;
            }
            
            boolean hasReplacement = false;
            String originalText = text;
            
            for (Map.Entry<String, String> entry : replacements.entrySet()) {
                if (entry.getKey() != null && text.contains(entry.getKey())) {
                    String replacementValue = entry.getValue() != null ? entry.getValue() : "";
                    text = text.replace(entry.getKey(), replacementValue);
                    hasReplacement = true;
                    logger.info("替换文本: {} -> {}", entry.getKey(), replacementValue);
                }
            }

            if (hasReplacement) {
                logger.info("开始应用文本替换，原文本长度: {}, 新文本长度: {}", originalText.length(), text.length());
                
                // 清除原有内容
                try {
                    for (int i = paragraph.getRuns().size() - 1; i >= 0; i--) {
                        paragraph.removeRun(i);
                    }
                } catch (Exception e) {
                    logger.error("清除段落Run时出错: {}", e.getMessage(), e);
                    return;
                }

                try {
                    CTP ctp = paragraph.getCTP();
                    if (ctp == null) {
                        logger.error("无法获取段落CTP，跳过格式设置");
                        // 仍然尝试添加文本
                        XWPFRun newRun = paragraph.createRun();
                        newRun.setText(text);
                        return;
                    }
                    
                    CTPPr ppr = ctp.isSetPPr() ? ctp.getPPr() : ctp.addNewPPr();
                    
                    // 设置行距
                    CTSpacing spacing = ppr.isSetSpacing() ? ppr.getSpacing() : ppr.addNewSpacing();
                    spacing.setLineRule(STLineSpacingRule.EXACT);
                    spacing.setLine(BigInteger.valueOf(560));
                    spacing.setAfter(BigInteger.valueOf(0));
                    spacing.setBefore(BigInteger.valueOf(0));
                    
                    // 设置首行缩进
                    org.openxmlformats.schemas.wordprocessingml.x2006.main.CTInd ind = 
                        ppr.isSetInd() ? ppr.getInd() : ppr.addNewInd();
                    ind.setFirstLine(BigInteger.valueOf(640));
                    
                } catch (Exception e) {
                    logger.error("设置段落格式时出错: {}", e.getMessage(), e);
                    // 继续处理，不中断文本添加
                }
                
                // 添加新文本
                try {
                    XWPFRun newRun = paragraph.createRun();
                    newRun.setText(text);
                    newRun.setFontFamily("仿宋");
                    newRun.setFontSize(16);
                    logger.debug("成功添加替换后的文本");
                } catch (Exception e) {
                    logger.error("添加新文本时出错: {}", e.getMessage(), e);
                }
            } else {
                logger.debug("段落无需替换: {}", text.length() > 50 ? text.substring(0, 50) + "..." : text);
            }
        } catch (Exception e) {
            logger.error("replaceInParagraph 方法执行出错: {}", e.getMessage(), e);
        }
    }



    /**
     * 在jobAsk段落后添加unitName和time
     */
    private void addUnitNameAndTimeAfterJobAsk(XWPFDocument document, XWPFParagraph jobAskParagraph, Map<String, String> templateData) {
        // 创建包含unitName和time的段落，左对齐
        XWPFParagraph unitTimeParagraph = document.createParagraph();
        unitTimeParagraph.setAlignment(ParagraphAlignment.LEFT);

        String unitName = templateData.get("${unitName}");
        String time = templateData.get("${time}");

        XWPFRun unitTimeRun = unitTimeParagraph.createRun();
        String unitTimeText = String.format("%s\n%s",
            StringUtils.isNotEmpty(unitName) ? unitName : "",
            StringUtils.isNotEmpty(time) ? time : "");
        unitTimeRun.setText(unitTimeText);
        unitTimeRun.setFontFamily("方正楷体_GB2312");
        unitTimeRun.setFontSize(16);
        unitTimeRun.setBold(false);
    }



    /**
     * 按顺序处理特殊占位符
     */
    private void processSpecialPlaceholders(XWPFDocument document, EmergencyEventDetailVO vo, Map<String, String> templateData, String eventLevel, String eventId) {
        log.info("🚀🚀🚀 === 开始处理特殊占位符 === 🚀🚀🚀");
        log.info("📋 emerPlanId: {}", vo.getEmerPlanId());
        log.info("📋 templateData中的jobAsk: {}", templateData.get("${jobAsk}"));
        
        // 处理 ${organization} 占位符
        if (StringUtils.isNotEmpty(vo.getEmerPlanId())) {
            log.info("✅ emerPlanId不为空，开始处理组织机构");
            EmPrePlanDept queryDept = new EmPrePlanDept();
            queryDept.setPrePlanId(vo.getEmerPlanId());
            queryDept.setEventLevel(eventLevel);
            List<EmPrePlanDeptDTO> deptList = iEmPrePlanDeptService.selectEmPrePlanDeptListWithTree(queryDept);
            log.info("📊 查询到组织机构数据: {} 条", deptList != null ? deptList.size() : 0);

            replaceOrganizationContent(document, deptList);
            
            //获取专家数据
            EmergencyEventRelationsVo info = emergencyEventRelationsService.getInfoByEventId(eventId);
            if(null != info ){
                List<ExpertInfoVO> expertInfoVOList = info.getExpertInfoVOList();
                if(expertInfoVOList != null && !expertInfoVOList.isEmpty()) {
                    log.info("✅ 找到专家数据: {} 条", expertInfoVOList.size());
                    // 在组织机构后添加专家信息
                    insertExpertInfoAfterOrganization(document, expertInfoVOList);
                } else {
                    log.info("⚠️ 专家数据为空，跳过专家信息添加");
                }
            } else {
                log.info("⚠️ 未找到应急事件关联信息，跳过专家信息添加");
            }
            
            // 在organization处理完成后，添加一个新页面，确保后续内容不会挤在一起
            int currentParagraphCount = document.getParagraphs().size();
            log.info("📊 处理组织机构后段落数: {}", currentParagraphCount);
            if (currentParagraphCount > 40) { // 如果段落数太多，添加分页符
                addPageBreak(document);
                log.info("📄 添加了分页符");
            }
        } else {
            log.warn("⚠️ emerPlanId为空，跳过组织机构处理");
        }
        
        // 添加后续内容部分（通常是工作要求，但支持其他内容）
        String jobAsk = templateData.get("${jobAsk}");
        log.info("🔍🔍🔍 开始处理后续内容部分 🔍🔍🔍");
        log.info("📋 jobAsk是否为null: {}", jobAsk == null);
        log.info("📋 jobAsk是否为空字符串: {}", StringUtils.isEmpty(jobAsk));
        if (jobAsk != null) {
            log.info("📋 jobAsk长度: {}", jobAsk.length());
            log.info("📋 jobAsk前100字符: '{}'", jobAsk.length() > 100 ? jobAsk.substring(0, 100) + "..." : jobAsk);
        } else {
            log.warn("⚠️ jobAsk为null！");
        }
        
        if (StringUtils.isNotEmpty(jobAsk)) {
            log.info("✅ jobAsk不为空，开始处理");
            
            // 🎯 在创建"二、工作要求"标题之前，先优化组织机构末尾的间距
            log.info("🔧 === 优化组织机构末尾与工作要求标题间的间距 ===");
            optimizeOrganizationEndSpacing(document);
            
            // 检查内容是否已经包含标题
            boolean hasTitle = jobAskContentContainsTitle(jobAsk);
            log.info("🔍 jobAsk内容是否已包含标题: {}", hasTitle);
            
            // 记录当前文档段落数量
            int paragraphCountBefore = document.getParagraphs().size();
            log.info("📊 处理jobAsk前文档段落数: {}", paragraphCountBefore);
            
            // 如果内容没有标题，先创建固定标题"二、工作要求"
            if (!hasTitle) {
                log.info("🔧 内容无标题，创建固定标题'二、工作要求'...");
                XWPFParagraph jobAskTitleParagraph = document.createParagraph();
                
                jobAskTitleParagraph.setStyle("2"); // 设置为标题2
                jobAskTitleParagraph.setAlignment(ParagraphAlignment.LEFT); // 左对齐
                setParagraphSpacing(jobAskTitleParagraph, 0, 3); // 段前0磅，段后3磅 - 紧贴前面内容
                
                // 设置行距为单倍行距（确保文字可见）
                setLineSpacing(jobAskTitleParagraph, 1.0); // 单倍行距，确保文字可见
                
                XWPFRun jobAskTitleRun = jobAskTitleParagraph.createRun();
                jobAskTitleRun.setText("二、工作要求");
                jobAskTitleRun.setFontFamily("方正楷体_GB2312");
                jobAskTitleRun.setFontSize(16);
                jobAskTitleRun.setBold(true);
                
                log.info("✅ 固定标题'二、工作要求'创建完成");
            } else {
                log.info("📋 内容已包含标题，跳过标题创建");
            }
            
            int paragraphCountAfterTitle = document.getParagraphs().size();
            log.info("📊 标题处理后文档段落数: {} (新增: {})", paragraphCountAfterTitle, paragraphCountAfterTitle - paragraphCountBefore);
            
            // 处理jobAsk内容，支持层次化显示
            log.info("📋 开始处理jobAsk内容部分...");
            processJobAskContent(document, jobAsk);
            
            int paragraphCountAfterContent = document.getParagraphs().size();
            log.info("📊 添加内容后文档段落数: {} (新增: {})", paragraphCountAfterContent, paragraphCountAfterTitle);
            
            // 简化间距处理：只调整间距，不做任何删除操作
            log.info("🔧 开始简化间距处理...");
            simpleSpacingAdjustment(document);
            
            int paragraphCountFinal = document.getParagraphs().size();
            log.info("📊 最终文档段落数: {} (变化: {})", paragraphCountFinal, paragraphCountFinal - paragraphCountAfterContent);
            
            if (paragraphCountFinal < paragraphCountAfterContent) {
                log.error("❌❌❌ 警告：间距处理删除了段落！！！");
            }
        } else {
            log.warn("⚠️⚠️⚠️ jobAsk为空，跳过后续内容处理 ⚠️⚠️⚠️");
            log.warn("⚠️ 这意味着系统配置'emNoticeJobAsk'可能没有设置或为空！");
            log.warn("⚠️ 请检查系统配置表sys_config中的emNoticeJobAsk配置！");
            log.warn("⚠️ 因此不会创建'二、工作要求'标题，debugOrganizationToJobAskGap会找不到！");
            
            // 为了避免间距问题，即使配置为空也创建一个默认的"二、工作要求"标题
            log.info("🔧 创建默认的'二、工作要求'标题以解决间距问题...");
            XWPFParagraph defaultJobAskTitle = document.createParagraph();
            defaultJobAskTitle.setStyle("2");
            defaultJobAskTitle.setAlignment(ParagraphAlignment.LEFT);
                         setParagraphSpacing(defaultJobAskTitle, 0, 3); // 段前0磅，段后3磅 - 紧贴前面内容
             setLineSpacing(defaultJobAskTitle, 1.0); // 单倍行距，确保文字可见
            
            XWPFRun defaultTitleRun = defaultJobAskTitle.createRun();
            defaultTitleRun.setText("二、工作要求");
            defaultTitleRun.setFontFamily("方正楷体_GB2312");
            defaultTitleRun.setFontSize(16);
            defaultTitleRun.setBold(true);
            log.info("✅ 默认标题创建完成");
            
            // 简单的间距调整
            simpleSpacingAdjustment(document);
        }
        
        // 添加特此通知、单位名称和时间（按顺序在左下角）
        addNoticeAndFooterInfo(document, templateData);
        
        // 在所有内容处理完成后，分析${organization}与后续内容间的间隙
        debugOrganizationToJobAskGap(document);
    }

    /**
     * 优化组织机构末尾的间距，确保与"二、工作要求"标题紧密连接
     */
    private void optimizeOrganizationEndSpacing(XWPFDocument document) {
        log.info("🔧 开始优化组织机构末尾间距...");
        
        List<XWPFParagraph> paragraphs = document.getParagraphs();
        int organizationEndIndex = findLastOrganizationParagraph(paragraphs);
        
        if (organizationEndIndex != -1) {
            XWPFParagraph orgEndParagraph = paragraphs.get(organizationEndIndex);
            String orgEndText = orgEndParagraph.getText();
            
            log.info("🎯 找到组织机构末尾段落[{}]: '{}'", organizationEndIndex, 
                    orgEndText != null ? (orgEndText.length() > 50 ? orgEndText.substring(0, 50) + "..." : orgEndText) : "(空)");
            
            // 获取当前间距信息
            String currentSpacing = getSpacingInfo(orgEndParagraph);
            log.info("📊 当前末尾段落间距: {}", currentSpacing);
            
            // 将组织机构末尾段落的段后间距设置为0
            setParagraphSpacing(orgEndParagraph, getParagraphBeforeSpacing(orgEndParagraph), 0);
            log.info("✅ 已将组织机构末尾段落的段后间距设置为0磅");
            
            // 清理末尾段落之后的空白段落
            cleanEmptyParagraphsAfterOrganization(document, organizationEndIndex);
            
        } else {
            log.warn("⚠️ 未找到组织机构末尾段落，无法优化间距");
        }
        
        log.info("🔧 组织机构末尾间距优化完成");
    }
    
    /**
     * 查找组织机构的最后一个段落
     */
    private int findLastOrganizationParagraph(List<XWPFParagraph> paragraphs) {
        log.info("🔍 查找组织机构的最后一个段落...");
        
        // 从后往前查找，找到最后一个组织机构相关的段落
        for (int i = paragraphs.size() - 1; i >= 0; i--) {
            String text = paragraphs.get(i).getText();
            if (text != null && !text.trim().isEmpty()) {
                log.info("🔍 检查段落[{}]: '{}'", i, text.length() > 50 ? text.substring(0, 50) + "..." : text);
                
                // 检查是否是组织机构相关内容
                if (isOrganizationContent(text)) {
                    log.info("✅ 找到组织机构末尾段落[{}]: '{}'", i, text.length() > 50 ? text.substring(0, 50) + "..." : text);
                    return i;
                }
                
                // 如果遇到其他章节的开始，说明组织机构部分已经结束
                if (isNextSectionStart(text)) {
                    log.info("🔍 遇到其他章节开始，停止查找");
                    break;
                }
            }
        }
        
        log.warn("❌ 未找到组织机构末尾段落");
        return -1;
    }
    
    /**
     * 删除组织机构后的空白段落（模拟backspace操作）
     */
    private void cleanEmptyParagraphsAfterOrganization(XWPFDocument document, int organizationEndIndex) {
        log.info("🧹 删除组织机构末尾后的空白段落（模拟用户backspace操作）...");
        
        List<XWPFParagraph> paragraphsToRemove = new ArrayList<>();
        List<XWPFParagraph> paragraphs = document.getParagraphs();
        
        // 从组织机构末尾的下一个段落开始检查
        for (int i = organizationEndIndex + 1; i < paragraphs.size(); i++) {
            XWPFParagraph paragraph = paragraphs.get(i);
            String text = paragraph.getText();
            
            // 如果是空白段落，标记为待删除
            if (text == null || text.trim().isEmpty()) {
                log.info("🧹 发现空白段落[{}]，标记为待删除", i);
                paragraphsToRemove.add(paragraph);
            } else {
                // 遇到有内容的段落，停止清理
                log.info("🔍 遇到有内容的段落[{}]: '{}'，停止清理", i, text.length() > 30 ? text.substring(0, 30) + "..." : text);
                break;
            }
        }
        
        // 执行删除操作
        int deletedCount = 0;
        for (XWPFParagraph paragraphToRemove : paragraphsToRemove) {
            try {
                // 安全删除段落
                document.removeBodyElement(document.getPosOfParagraph(paragraphToRemove));
                deletedCount++;
                log.info("✅ 成功删除空白段落 {}/{}", deletedCount, paragraphsToRemove.size());
            } catch (Exception e) {
                log.warn("⚠️ 删除空白段落失败: {}", e.getMessage());
            }
        }
        
        log.info("🧹 清理完成，成功删除了{}个空白段落", deletedCount);
        
        // 验证删除效果
        List<XWPFParagraph> updatedParagraphs = document.getParagraphs();
        log.info("📊 删除后文档段落总数: {}", updatedParagraphs.size());
    }

    /**
     * 检查jobAsk内容是否已经包含标题
     */
    private boolean jobAskContentContainsTitle(String content) {
        if (content == null || content.trim().isEmpty()) {
            log.info("🔍 内容为空，无标题");
            return false;
        }
        
        log.info("🔍 检查内容是否包含标题，内容长度: {}", content.length());
        log.info("📝 内容前100字符: '{}'", content.length() > 100 ? content.substring(0, 100) + "..." : content);
        
        // 按行分割检查是否有标题格式
        String[] lines = content.split("\\n|\\r\\n");
        int checkedLines = 0;
        for (String line : lines) {
            line = line.trim();
            if (StringUtils.isEmpty(line)) {
                continue; // 跳过空行，继续检查下一行
            }
            
            checkedLines++;
            log.info("🔍 检查第{}行非空内容: '{}'", checkedLines, line.length() > 50 ? line.substring(0, 50) + "..." : line);
            
            // 检查是否以中文数字标题开头（如：一、二、三、）
            if (line.matches("^[一二三四五六七八九十]+、.*")) {
                log.info("✅ 发现中文标题格式: '{}'", line);
                return true;
            }
            
            // 检查是否以阿拉伯数字标题开头（如：1、2、3、）
            // 注意：1、2、3、这种格式通常是内容条目，不是章节标题，所以暂时注释掉
            // if (line.matches("^\\d+、.*")) {
            //     log.info("✅ 发现数字标题格式: '{}'", line);
            //     return true;
            // }
            
            // 检查是否直接包含"工作要求"等标题字样
            if (line.contains("工作要求") && line.length() < 20) {
                log.info("✅ 发现工作要求标题: '{}'", line);
                return true;
            }
            
            // 只检查前3行非空内容，避免误判内容中的标题
            if (checkedLines >= 3) {
                log.info("🔍 已检查前3行非空内容，停止检查");
                break;
            }
        }
        
        log.info("❌ 内容中未发现标题格式");
        return false;
    }

    /**
     * 处理jobAsk内容，智能处理包含标题的情况
     */
    private void processJobAskContent(XWPFDocument document, String jobAsk) {
        if (StringUtils.isEmpty(jobAsk)) {
            log.warn("⚠️ jobAsk内容为空，跳过处理");
            return;
        }
        
        log.info("📋 开始处理jobAsk内容，内容长度: {}", jobAsk.length());
        log.info("📋 内容预览: '{}'", jobAsk.length() > 200 ? jobAsk.substring(0, 200) + "..." : jobAsk);
        
        // 检查内容是否包含标题格式
        boolean contentHasTitle = jobAskContentContainsTitle(jobAsk);
        log.info("🔍 内容是否包含标题格式: {}", contentHasTitle);
        
        if (contentHasTitle) {
            // 如果内容已包含标题，直接按行处理，保持原有格式
            log.info("📝 内容包含标题，按行处理保持格式");
            String[] lines = jobAsk.split("\\n|\\r\\n");
            
            for (String line : lines) {
                if (StringUtils.isNotEmpty(line.trim())) {
                    // 判断是否是标题行
                    if (isJobAskTitleLine(line.trim())) {
                        log.info("🏷️ 创建标题段落: '{}'", line.trim());
                        createJobAskTitleParagraph(document, line.trim());
                    } else {
                        log.info("📝 创建内容段落: '{}'", line.trim().length() > 50 ? line.trim().substring(0, 50) + "..." : line.trim());
                        createJobAskParagraph(document, line.trim());
                    }
                }
            }
        } else {
            // 内容没有标题格式，尝试智能分割
            log.info("📝 内容无标题格式，尝试智能分割");
            
            // 先尝试按照数字编号分割内容（如 "1、"、"2、"、"3、"等）
            String[] sections = jobAsk.split("(?=\\d+[、．.])");
            log.info("📝 按数字编号分割结果: {} 个部分", sections.length);
            
            if (sections.length > 1) {
                // 有数字编号的情况，逐个处理
                log.info("📝 多部分处理模式，共 {} 个部分", sections.length);
                for (int i = 0; i < sections.length; i++) {
                    String section = sections[i];
                    if (StringUtils.isNotEmpty(section.trim())) {
                        log.info("📝 创建编号段落 {}: '{}'", i + 1, section.trim().length() > 50 ? section.trim().substring(0, 50) + "..." : section.trim());
                        createJobAskParagraph(document, section.trim());
                    }
                }
            } else {
                // 单一内容，按行分割并添加编号
                log.info("📝 单一内容，按行分割并添加编号");
                String[] lines = jobAsk.split("\\n|\\r\\n");
                int count = 1;
                for (String line : lines) {
                    if (StringUtils.isNotEmpty(line.trim())) {
                        log.info("📝 创建自动编号段落 {}: '{}'", count, line.trim().length() > 50 ? line.trim().substring(0, 50) + "..." : line.trim());
                        createJobAskParagraph(document, count + "、" + line.trim());
                        count++;
                    }
                }
            }
        }
    }
    
    /**
     * 判断是否是jobAsk的标题行
     */
    private boolean isJobAskTitleLine(String line) {
        if (StringUtils.isEmpty(line)) {
            return false;
        }
        
        line = line.trim();
        
        // 检查是否以中文数字标题开头（如：一、二、三、）
        if (line.matches("^[一二三四五六七八九十]+、.*")) {
            return true;
        }
        
        // 检查是否以阿拉伯数字标题开头（如：1、2、3、）
        // 注意：1、2、3、这种格式通常是内容条目，不是章节标题
        // if (line.matches("^\\d+、.*")) {
        //     return true;
        // }
        
        // 检查是否是短的标题行（如"工作要求"）
        if (line.contains("工作要求") && line.length() < 20) {
            return true;
        }
        
        return false;
    }
    
    /**
     * 创建jobAsk的标题段落
     */
    private void createJobAskTitleParagraph(XWPFDocument document, String titleText) {
        log.info("🏷️ 创建jobAsk标题段落: '{}'", titleText);
        
        XWPFParagraph titleParagraph = document.createParagraph();
        titleParagraph.setStyle("3"); // 设置为标题3
        titleParagraph.setAlignment(ParagraphAlignment.LEFT); // 左对齐
        setParagraphSpacing(titleParagraph, 6, 3); // 段前6磅，段后3磅
        setLineSpacing(titleParagraph, 1.0); // 单倍行距
        
        XWPFRun titleRun = titleParagraph.createRun();
        titleRun.setText(titleText);
        titleRun.setFontFamily("方正楷体_GB2312");
        titleRun.setFontSize(16);
        titleRun.setBold(true);
        
        log.info("✅ jobAsk标题段落创建成功");
    }
    
    /**
     * 创建jobAsk内容段落
     */
    private void createJobAskParagraph(XWPFDocument document, String content) {
        log.info("🔧 开始创建内容段落: '{}'", content.length() > 30 ? content.substring(0, 30) + "..." : content);
        
        // 创建段落
        XWPFParagraph contentParagraph = document.createParagraph();
        contentParagraph.setAlignment(ParagraphAlignment.LEFT); // 左对齐
        setParagraphSpacing(contentParagraph, 3, 3); // 段前3磅，段后3磅
        
        // 设置首行缩进2个字符（1个中文字符约等于16磅，所以2个字符=32磅=640twips）
        setFirstLineIndent(contentParagraph, 640); // 2个字符的缩进
        
        // 添加内容
        XWPFRun contentRun = contentParagraph.createRun();
        contentRun.setText(content);
        contentRun.setFontFamily("方正仿宋_GB2312");
        contentRun.setFontSize(16); // 三号字体约16磅
        contentRun.setBold(false);
        
        log.info("✅ 内容段落创建成功");
    }
    
    /**
     * 设置段落首行缩进
     * @param paragraph 段落
     * @param twips 缩进量（以twips为单位，1磅=20twips）
     */
    private void setFirstLineIndent(XWPFParagraph paragraph, int twips) {
        CTP ctp = paragraph.getCTP();
        CTPPr ppr = ctp.isSetPPr() ? ctp.getPPr() : ctp.addNewPPr();
        
        // 设置缩进
        org.openxmlformats.schemas.wordprocessingml.x2006.main.CTInd ind = 
            ppr.isSetInd() ? ppr.getInd() : ppr.addNewInd();
        
        // 设置首行缩进
        ind.setFirstLine(BigInteger.valueOf(twips));
    }

    /**
     * 为整个文档设置全文首行缩进2个字符
     * @param document Word文档
     */
    private void setGlobalFirstLineIndent(XWPFDocument document) {
        List<XWPFParagraph> paragraphs = document.getParagraphs();
        int processedCount = 0;
        int skippedCount = 0;
        
        for (XWPFParagraph paragraph : paragraphs) {
            String text = paragraph.getText();
            if (text != null && !text.trim().isEmpty()) {
                // 跳过已经设置了缩进的段落和标题段落
                boolean isTitle = isTitle(paragraph);
                boolean hasIndent = hasFirstLineIndent(paragraph);
                
                if (!isTitle && !hasIndent) {
                    // 为正文段落设置首行缩进2个字符（640 twips）
                    setFirstLineIndent(paragraph, 640);
                    processedCount++;
                } else {
                    skippedCount++;
                }
            }
        }
        
        log.info("全文首行缩进设置完成 - 处理段落: {}, 跳过段落: {}", processedCount, skippedCount);
    }

    /**
     * 判断段落是否是标题
     */
    private boolean isTitle(XWPFParagraph paragraph) {
        String style = paragraph.getStyle();
        if (style != null && (style.equals("1") || style.equals("2") || style.equals("3"))) {
            return true;
        }
        
        String text = paragraph.getText();
        if (text != null) {
            // 检查是否包含标题特征
            if (text.contains("一、") || text.contains("二、") || text.contains("三、") ||
                text.contains("（一）") || text.contains("（二）") || text.contains("（三）") ||
                text.matches("^\\d+、.*") || text.contains("主要职责") ||
                text.contains("组成人员") || text.contains("应急指挥机构")) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * 检查段落是否已经设置了首行缩进
     */
    private boolean hasFirstLineIndent(XWPFParagraph paragraph) {
        CTP ctp = paragraph.getCTP();
        if (ctp.isSetPPr()) {
            CTPPr ppr = ctp.getPPr();
            if (ppr.isSetInd()) {
                org.openxmlformats.schemas.wordprocessingml.x2006.main.CTInd ind = ppr.getInd();
                return ind.isSetFirstLine() && ind.getFirstLine().intValue() > 0;
            }
        }
        return false;
    }

    /**
     * 添加特此通知、单位名称和时间信息
     */
    private void addNoticeAndFooterInfo(XWPFDocument document, Map<String, String> templateData) {
        // 添加一些空行间距
        XWPFParagraph spaceParagraph = document.createParagraph();
        setParagraphSpacing(spaceParagraph, 12, 6); // 段前12磅，段后6磅
        
        // 1. 添加"特此通知"
        XWPFParagraph noticeParagraph = document.createParagraph();
        noticeParagraph.setAlignment(ParagraphAlignment.LEFT); // 左对齐
        setParagraphSpacing(noticeParagraph, 6, 6); // 段前6磅，段后6磅
        setFirstLineIndent(noticeParagraph, 640); // 设置首行缩进2个字符
        XWPFRun noticeRun = noticeParagraph.createRun();
        noticeRun.setText("特此通知。");
        noticeRun.setFontFamily("方正仿宋_GB2312");
        noticeRun.setFontSize(16);
        noticeRun.setBold(false);
        
        // 2. 添加单位名称
        String unitName = templateData.get("${unitName}");
        if (StringUtils.isNotEmpty(unitName)) {
            // 添加一些间距
            XWPFParagraph spaceBeforeUnit = document.createParagraph();
            setParagraphSpacing(spaceBeforeUnit, 12, 3); // 段前12磅，段后3磅
            
            XWPFParagraph unitParagraph = document.createParagraph();
            unitParagraph.setAlignment(ParagraphAlignment.LEFT); // 左对齐
            setParagraphSpacing(unitParagraph, 6, 6); // 段前6磅，段后6磅 - 与特此通知保持一致
            setFirstLineIndent(unitParagraph, 640); // 设置首行缩进2个字符
            XWPFRun unitRun = unitParagraph.createRun();
            unitRun.setText(unitName); // 去掉空格缩进，改用格式化缩进
            unitRun.setFontFamily("方正仿宋_GB2312");
            unitRun.setFontSize(16);
            unitRun.setBold(false);
        }
        
        // 3. 添加时间
        String time = templateData.get("${time}");
        if (StringUtils.isNotEmpty(time)) {
            XWPFParagraph timeParagraph = document.createParagraph();
            timeParagraph.setAlignment(ParagraphAlignment.LEFT); // 左对齐
            setParagraphSpacing(timeParagraph, 6, 6); // 段前6磅，段后6磅 - 与特此通知保持一致
            setFirstLineIndent(timeParagraph, 640); // 设置首行缩进2个字符
            XWPFRun timeRun = timeParagraph.createRun();
            timeRun.setText(time); // 去掉空格缩进，改用格式化缩进
            timeRun.setFontFamily("方正仿宋_GB2312");
            timeRun.setFontSize(16);
            timeRun.setBold(false);
        }
    }



    /**
     * 计算主要职责标题的段前间距
     * @param dept 部门信息
     * @return 段前间距（磅）
     */
    private int calculateDutyTitleSpacing(EmPrePlanDeptDTO dept) {
        String deptName = dept.getDeptName();
        
        // 特殊处理：专家组的主要职责使用较小间距
        if (deptName != null && deptName.contains("专家组")) {
            log.debug("专家组 '{}' 的主要职责使用较小间距: 4磅", deptName);
            return 4; // 专家组的主要职责使用4磅间距
        }
        
        // 其他部门的主要职责使用标准间距
        return 6; // 比原来的8磅稍微小一点
    }

    /**
     * 计算主要职责内容的段前间距
     * @param dept 部门信息
     * @return 段前间距（磅）
     */
    private int calculateDutyContentSpacing(EmPrePlanDeptDTO dept) {
        String deptName = dept.getDeptName();
        
        // 特殊处理：专家组的主要职责内容使用更小间距
        if (deptName != null && deptName.contains("专家组")) {
            log.debug("专家组 '{}' 的主要职责内容使用最小间距: 2磅", deptName);
            return 2; // 专家组的主要职责内容使用2磅间距
        }
        
        // 其他部门的主要职责内容使用标准间距
        return 3; // 比原来的4磅稍微小一点
    }

    /**
     * 根据部门类型动态计算主要职责内容的段后间距
     * @param dept 部门信息
     * @return 段后间距（磅）
     */
    private int calculateDutyContentAfterSpacing(EmPrePlanDeptDTO dept) {
        String deptName = dept.getDeptName();
        
        log.info("🔍 计算段后间距 - 部门名称: '{}'", deptName);
        
        // 直接取消所有间距，设置为0磅
        log.info("🚫 取消间距测试 - 部门 '{}' - 段后间距设置为0磅", deptName);
        return 0; // 直接取消间距，设置为0磅
    }

    /**
     * 重复字符串（兼容Java 8）
     */
    private String repeatString(String str, int count) {
        if (count <= 0) {
            return "";
        }
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < count; i++) {
            sb.append(str);
        }
        return sb.toString();
    }

    /**
     * 清理"现将有关事项通知如下"后面的空段落
     */
    private void cleanEmptyParagraphsAfterNotice(XWPFDocument document) {
        List<XWPFParagraph> paragraphs = document.getParagraphs();
        int noticeIndex = -1;
        
        // 找到"现将有关事项通知如下"的位置
        for (int i = 0; i < paragraphs.size(); i++) {
            XWPFParagraph paragraph = paragraphs.get(i);
            String text = paragraph.getText();
            if (text != null && text.contains("现将有关事项通知如下")) {
                noticeIndex = i;
                log.info("找到'现将有关事项通知如下'在第{}个段落", i + 1);
                break;
            }
        }
        
        if (noticeIndex >= 0) {
            // 从后往前删除空段落，避免索引问题
            for (int i = paragraphs.size() - 1; i > noticeIndex; i--) {
                XWPFParagraph paragraph = paragraphs.get(i);
                String text = paragraph.getText();
                
                // 如果是空段落或只包含空白字符
                if (text == null || text.trim().isEmpty()) {
                    log.debug("删除空段落: 位置[{}], 内容: '{}'", i, text);
                    document.removeBodyElement(i);
                } 
                // 如果包含${organization}占位符，也删除
                else if (text.contains("${organization}")) {
                    log.debug("删除包含organization占位符的段落: 位置[{}]", i);
                    document.removeBodyElement(i);
                }
            }
            log.info("清理完成，删除了'现将有关事项通知如下'后面的空段落和占位符段落");
        }
    }

    /**
     * 调试：打印清理后的文档结构
     */
    private void debugDocumentStructure(XWPFDocument document) {
        log.info("=== 当前文档结构 ===");
        List<XWPFParagraph> paragraphs = document.getParagraphs();
        for (int i = 0; i < paragraphs.size(); i++) {
            XWPFParagraph paragraph = paragraphs.get(i);
            String text = paragraph.getText();
            log.info("段落[{}]: '{}'", i, text != null ? text : "(空)");
        }
        log.info("=== 文档结构调试完成 ===");
    }

    /**
     * 专门清理柳江区应急指挥机构主要职责内容后面的多余空白
     */
    private void cleanExtraContentAfterLiujiangDutyInfo(XWPFDocument document) {
        log.info("=== 开始清理柳江区应急指挥机构主要职责内容后面的多余空白 ===");
        
        List<XWPFParagraph> paragraphs = document.getParagraphs();
        int liujiangDutyContentIndex = -1;
        int workRequirementIndex = -1;
        
        // 查找柳江区应急指挥机构的主要职责内容段落
        for (int i = 0; i < paragraphs.size(); i++) {
            XWPFParagraph paragraph = paragraphs.get(i);
            String text = paragraph.getText();
            
            if (text != null) {
                // 查找柳江区应急指挥机构标题，然后定位其主要职责内容
                if (text.contains("柳江区应急指挥机构")) {
                    log.info("找到柳江区应急指挥机构标题段落[{}]: '{}'", i, text);
                    // 查找后续的主要职责内容段落
                    if (i + 2 < paragraphs.size()) {
                        XWPFParagraph possibleDutyContent = paragraphs.get(i + 2);
                        String dutyText = possibleDutyContent.getText();
                        if (dutyText != null && (dutyText.contains("1、") || dutyText.length() > 20)) {
                            liujiangDutyContentIndex = i + 2;
                            log.info("找到柳江区应急指挥机构主要职责内容段落[{}]: '{}'", i + 2, 
                                    dutyText.length() > 50 ? dutyText.substring(0, 50) + "..." : dutyText);
                        }
                    }
                }
                
                // 查找"二、工作要求"段落
                if (text.contains("二、工作要求")) {
                    workRequirementIndex = i;
                    log.info("找到'二、工作要求'段落[{}]: '{}'", i, text);
                }
            }
        }
        
        // 如果找到了两个关键段落，清理中间的多余空白
        if (liujiangDutyContentIndex != -1 && workRequirementIndex != -1 && liujiangDutyContentIndex < workRequirementIndex) {
            log.info("柳江区主要职责内容段落[{}]到'二、工作要求'段落[{}]之间有{}个段落", 
                     liujiangDutyContentIndex, workRequirementIndex, workRequirementIndex - liujiangDutyContentIndex - 1);
            
            List<Integer> paragraphsToRemove = new ArrayList<>();
            
            // 检查中间的段落，标记需要删除的空白段落
            for (int i = liujiangDutyContentIndex + 1; i < workRequirementIndex; i++) {
                XWPFParagraph paragraph = paragraphs.get(i);
                String text = paragraph.getText();
                
                // 如果是空段落或只包含空白字符的段落，标记删除
                if (text == null || text.trim().isEmpty() || text.equals("\n") || text.equals("\r\n")) {
                    paragraphsToRemove.add(i);
                    log.info("标记删除空白段落[{}]: '{}'", i, text != null ? text.replace("\n", "\\n").replace("\r", "\\r") : "(null)");
                }
                // 如果是只包含少量换行符的段落，也标记删除
                else if (text.length() <= 3 && text.matches("[\r\n\\s]*")) {
                    paragraphsToRemove.add(i);
                    log.info("标记删除换行符段落[{}]: '{}'", i, text.replace("\n", "\\n").replace("\r", "\\r"));
                }
            }
            
            // 从后往前删除标记的段落，避免索引问题
            for (int i = paragraphsToRemove.size() - 1; i >= 0; i--) {
                int indexToRemove = paragraphsToRemove.get(i);
                try {
                    document.removeBodyElement(indexToRemove);
                    log.info("成功删除空白段落[{}]", indexToRemove);
                } catch (Exception e) {
                    log.warn("删除空白段落[{}]失败: {}", indexToRemove, e.getMessage());
                }
            }
            
            log.info("柳江区主要职责内容后面的空白清理完成，共删除{}个段落", paragraphsToRemove.size());
        } else {
            log.warn("未找到柳江区应急指挥机构主要职责内容或'二、工作要求'段落");
            log.warn("liujiangDutyContentIndex: {}, workRequirementIndex: {}", liujiangDutyContentIndex, workRequirementIndex);
        }
        
        log.info("=== 柳江区应急指挥机构主要职责内容后面的空白清理完成 ===");
    }

    /**
     * 极简的间距调整：只调整间距，绝对不删除任何内容
     */
    private void simpleSpacingAdjustment(XWPFDocument document) {
        log.info("🛡️🛡️🛡️ 开始极简间距调整（绝对不删除任何段落）🛡️🛡️🛡️");
        
        List<XWPFParagraph> paragraphs = document.getParagraphs();
        log.info("📊 当前文档段落总数: {}", paragraphs.size());
        
        for (int i = 0; i < paragraphs.size(); i++) {
            XWPFParagraph paragraph = paragraphs.get(i);
            String text = paragraph.getText();
            
            if (text != null && !text.trim().isEmpty()) {
                // 组织机构相关段落：设置段后间距为0
                if (text.contains("主要职责") || 
                    text.contains("柳江区应急指挥机构") ||
                    (text.contains("1、") && text.length() > 30) ||
                    (text.contains("应急指挥机构") && text.length() > 20)) {
                    
                    setParagraphSpacing(paragraph, getParagraphBeforeSpacing(paragraph), 0);
                    log.info("🎯 设置组织机构段落[{}]段后间距为0: '{}'", i, 
                            text.length() > 40 ? text.substring(0, 40) + "..." : text);
                }
                
                // 工作要求等标题段落：设置段前间距为0，最小行距
                if (text.contains("二、工作要求") || text.contains("工作要求") ||
                    text.matches("^[一二三四五六七八九十]+、.*")) {
                    
                    setParagraphSpacing(paragraph, 0, getParagraphAfterSpacing(paragraph));
                    setLineSpacing(paragraph, 1.0); // 单倍行距，确保文字可见
                    log.info("🎯 设置后续内容段落[{}]段前间距为0，最小行距: '{}'", i, text);
                }
            }
        }
        
        log.info("✅ 极简间距调整完成，段落数量保持: {}", paragraphs.size());
    }

    /**
     * 保守的间距调整：只调整间距，不删除内容，确保安全性
     */
    private void cleanOrganizationEndGap(XWPFDocument document) {
        log.info("=== 开始保守的间距调整（只调整间距，不删除内容）===");
        
        List<XWPFParagraph> paragraphs = document.getParagraphs();
        
        // 查找组织机构相关的最后段落
        int lastOrgIndex = -1;
        int workRequirementIndex = -1;
        
        for (int i = 0; i < paragraphs.size(); i++) {
            XWPFParagraph paragraph = paragraphs.get(i);
            String text = paragraph.getText();
            
            if (text != null) {
                // 查找组织机构相关内容
                if (text.contains("柳江区应急指挥机构")) {
                    // 查找其主要职责内容
                    for (int j = i + 1; j < Math.min(i + 5, paragraphs.size()); j++) {
                        String dutyText = paragraphs.get(j).getText();
                        if (dutyText != null && (dutyText.contains("1、") || dutyText.contains("主要职责") || dutyText.length() > 20)) {
                            lastOrgIndex = j;
                            log.info("🎯 找到柳江区应急指挥机构主要职责段落[{}]: '{}'", j, 
                                    dutyText.length() > 50 ? dutyText.substring(0, 50) + "..." : dutyText);
                            break;
                        }
                    }
                }
                // 通用查找：任何包含主要职责或应急指挥机构的段落
                else if (text.contains("主要职责") || 
                        (text.contains("应急指挥机构") && text.length() > 20) ||
                        (text.contains("1、") && text.length() > 30)) {
                    lastOrgIndex = i;
                    log.info("📍 找到组织机构相关段落[{}]: '{}'", i, 
                            text.length() > 40 ? text.substring(0, 40) + "..." : text);
                }
                
                // 查找工作要求或其他后续内容
                if (text.contains("二、工作要求") || text.contains("工作要求") ||
                    text.matches("^[一二三四五六七八九十]+、.*")) {
                    workRequirementIndex = i;
                    log.info("🎯 找到后续内容段落[{}]: '{}'", i, text);
                }
            }
        }
        
        // 只调整间距，不删除任何内容
        if (lastOrgIndex != -1) {
            XWPFParagraph lastOrgParagraph = paragraphs.get(lastOrgIndex);
            setParagraphSpacing(lastOrgParagraph, getParagraphBeforeSpacing(lastOrgParagraph), 0);
            log.info("✅ 已将组织机构段落[{}]的段后间距设为0磅", lastOrgIndex);
        }
        
        if (workRequirementIndex != -1) {
            XWPFParagraph workReqParagraph = paragraphs.get(workRequirementIndex);
            setParagraphSpacing(workReqParagraph, 0, getParagraphAfterSpacing(workReqParagraph));
            setLineSpacing(workReqParagraph, 1.0); // 单倍行距，确保文字可见
            log.info("✅ 已将后续内容段落[{}]的段前间距设为0磅，行距设为最小", workRequirementIndex);
        }
        
        log.info("=== 保守间距调整完成 ===");
    }
    
    /**
     * 查找组织机构内容的开始位置
     */
    private int findOrganizationStartIndex(List<XWPFParagraph> paragraphs) {
        for (int i = 0; i < paragraphs.size(); i++) {
            XWPFParagraph paragraph = paragraphs.get(i);
            String text = paragraph.getText();
            
            if (text != null && (
                text.contains("应急指挥机构") ||
                text.contains("一、") ||
                text.matches(".*（[一二三四五六七八九十]+）.*") // 匹配中文编号
            )) {
                log.debug("找到组织机构开始标志段落[{}]: '{}'", i, text);
                return i;
            }
        }
        return -1;
    }
    
    /**
     * 查找组织机构的最后一个内容段落
     */
    private int findLastOrganizationContentIndex(List<XWPFParagraph> paragraphs, int startIndex) {
        if (startIndex == -1) return -1;
        
        int lastContentIndex = -1;
        
        // 首先尝试精确匹配已知的组织机构模式
        for (int i = startIndex; i < paragraphs.size(); i++) {
            XWPFParagraph paragraph = paragraphs.get(i);
            String text = paragraph.getText();
            
            if (text != null && !text.trim().isEmpty()) {
                // 优先处理已知的具体情况（确保原有逻辑正常工作）
                if (text.contains("柳江区应急指挥机构")) {
                    // 查找柳江区应急指挥机构的主要职责内容
                    for (int j = i + 1; j < Math.min(i + 5, paragraphs.size()); j++) {
                        String dutyText = paragraphs.get(j).getText();
                        if (dutyText != null && (dutyText.contains("1、") || dutyText.contains("主要职责") || dutyText.length() > 20)) {
                            lastContentIndex = j;
                            log.info("精确匹配：找到柳江区应急指挥机构主要职责内容段落[{}]", j);
                            break;
                        }
                    }
                }
                
                // 通用判断是否属于组织机构内容
                if (isOrganizationContent(text)) {
                    lastContentIndex = i;
                    log.debug("更新组织机构末尾段落[{}]: '{}'", i, 
                            text.length() > 30 ? text.substring(0, 30) + "..." : text);
                } else if (isNextSectionStart(text)) {
                    // 如果遇到下一个章节开始，停止查找
                    log.debug("遇到下一章节开始[{}]: '{}'，停止查找组织机构内容", i, text);
                    break;
                }
            }
        }
        
        if (lastContentIndex != -1) {
            String text = paragraphs.get(lastContentIndex).getText();
            log.info("确定组织机构最后内容段落[{}]: '{}'", lastContentIndex, 
                    text.length() > 50 ? text.substring(0, 50) + "..." : text);
        }
        
        return lastContentIndex;
    }
    
    /**
     * 判断是否为组织机构相关内容
     */
    private boolean isOrganizationContent(String text) {
        return text.contains("应急指挥机构") ||
               text.contains("主要职责") ||
               text.contains("组  长") ||
               text.contains("副组长") ||
               text.contains("成  员") ||
               text.matches(".*（[一二三四五六七八九十]+）.*") ||  // 中文编号
               text.matches(".*\\d+、.*") ||  // 数字编号
               text.matches(".*（\\d+）.*") ||  // 数字括号编号
               text.contains("负责") ||
               text.contains("统筹") ||
               text.contains("协调") ||
               (text.length() > 20 && (text.contains("指挥") || text.contains("处置")));
    }
    
    /**
     * 判断是否为下一个章节的开始
     */
    private boolean isNextSectionStart(String text) {
        return text.matches("^[一二三四五六七八九十]+、.*") ||  // 以中文数字开头的标题
               text.matches("^\\d+、.*") ||  // 以数字开头的标题
               text.contains("工作要求") ||
               text.contains("职责分工") ||
               text.contains("应急响应") ||
               text.contains("处置措施") ||
               text.contains("应急保障") ||
               text.contains("相关要求") ||
               text.contains("工作措施") ||
               text.contains("工作部署") ||
               text.contains("实施步骤") ||
               text.contains("保障措施") ||
               // 通用的章节标题模式
               text.matches(".*[一二三四五六七八九十]+、.*") ||
               text.matches(".*\\d+、.*") ||
               (text.length() > 5 && text.length() < 20 && 
                (text.contains("、") || text.contains("：") || text.contains(":")));
    }
    
    /**
     * 查找组织机构后的下一个实际内容段落
     */
    private int findNextContentAfterOrganization(List<XWPFParagraph> paragraphs, int lastOrgIndex) {
        if (lastOrgIndex == -1) return -1;
        
        for (int i = lastOrgIndex + 1; i < paragraphs.size(); i++) {
            XWPFParagraph paragraph = paragraphs.get(i);
            String text = paragraph.getText();
            
            // 跳过空白段落
            if (text == null || text.trim().isEmpty() || 
                text.equals("\n") || text.equals("\r\n") ||
                text.matches("[\r\n\\s]*")) {
                continue;
            }
            
            // 找到第一个有实际内容的段落
            if (text.length() > 3) {
                log.info("找到组织机构后的下一个内容段落[{}]: '{}'", i, text);
                return i;
            }
        }
        
        return -1;
    }
    
    /**
     * 清理两个段落之间的空白段落
     */
    private void cleanGapBetweenParagraphs(XWPFDocument document, int startIndex, int endIndex) {
        List<XWPFParagraph> paragraphs = document.getParagraphs();
        List<Integer> paragraphsToRemove = new ArrayList<>();
        
        for (int i = startIndex + 1; i < endIndex; i++) {
            XWPFParagraph paragraph = paragraphs.get(i);
            String text = paragraph.getText();
            
            if (text == null || text.trim().isEmpty() || 
                text.equals("\n") || text.equals("\r\n") ||
                text.matches("[\r\n\\s]*")) {
                paragraphsToRemove.add(i);
                log.debug("标记删除空白段落[{}]: '{}'", i, 
                        text != null ? text.replace("\n", "\\n").replace("\r", "\\r") : "(null)");
            }
        }
        
        // 从后往前删除
        for (int i = paragraphsToRemove.size() - 1; i >= 0; i--) {
            int indexToRemove = paragraphsToRemove.get(i);
            try {
                document.removeBodyElement(indexToRemove);
                log.debug("成功删除空白段落[{}]", indexToRemove);
            } catch (Exception e) {
                log.warn("删除空白段落[{}]失败: {}", indexToRemove, e.getMessage());
            }
        }
        
        log.info("清理空白段落完成，共删除{}个段落", paragraphsToRemove.size());
    }
    
    /**
     * 优化下一个内容段落的间距和行距
     */
    private void optimizeNextContentSpacing(XWPFParagraph nextContentParagraph) {
        // 设置段前间距为0磅，段后间距保持原有设置
        int afterSpacing = getParagraphAfterSpacing(nextContentParagraph);
        setParagraphSpacing(nextContentParagraph, 0, afterSpacing);
        
        // 设置为单倍行距（确保文字可见）
        setLineSpacing(nextContentParagraph, 1.0);
        
        String text = nextContentParagraph.getText();
        log.info("已优化下一个内容段落的间距: 段前0磅, 最小行距 - '{}'", 
                text != null && text.length() > 30 ? text.substring(0, 30) + "..." : text);
    }
    
    /**
     * 获取段落的段后间距（磅数）
     */
    private int getParagraphAfterSpacing(XWPFParagraph paragraph) {
        try {
            CTP ctp = paragraph.getCTP();
            if (ctp != null && ctp.isSetPPr()) {
                CTPPr ppr = ctp.getPPr();
                if (ppr != null && ppr.isSetSpacing()) {
                    CTSpacing spacing = ppr.getSpacing();
                    if (spacing.isSetAfter()) {
                        return spacing.getAfter().intValue() / 20; // 转换为磅
                    }
                }
            }
            return 3; // 默认段后间距
        } catch (Exception e) {
            log.warn("获取段落段后间距失败: {}", e.getMessage());
            return 3;
        }
    }
    
    /**
     * 降级处理：当动态检测失败时使用原有逻辑
     */
    private void fallbackOrganizationGapClean(XWPFDocument document) {
        log.info("使用降级处理方案，回到原有的精确逻辑...");
        
        List<XWPFParagraph> paragraphs = document.getParagraphs();
        int liujiangDutyContentIndex = -1;
        int workRequirementIndex = -1;
        
        // 使用原有的精确查找逻辑
        for (int i = 0; i < paragraphs.size(); i++) {
            XWPFParagraph paragraph = paragraphs.get(i);
            String text = paragraph.getText();
            
            if (text != null) {
                // 精确查找柳江区应急指挥机构标题，然后定位其主要职责内容
                if (text.contains("柳江区应急指挥机构")) {
                    log.info("降级处理：找到柳江区应急指挥机构标题段落[{}]", i);
                    // 查找后续的主要职责内容段落
                    if (i + 2 < paragraphs.size()) {
                        XWPFParagraph possibleDutyContent = paragraphs.get(i + 2);
                        String dutyText = possibleDutyContent.getText();
                        if (dutyText != null && (dutyText.contains("1、") || dutyText.length() > 20)) {
                            liujiangDutyContentIndex = i + 2;
                            log.info("降级处理：找到柳江区应急指挥机构主要职责内容段落[{}]", i + 2);
                        }
                    }
                }
                
                // 查找"二、工作要求"或类似的下一章节段落
                if (text.contains("二、工作要求") || text.contains("工作要求") || 
                    text.matches("^[一二三四五六七八九十]+、.*")) {
                    workRequirementIndex = i;
                    log.info("降级处理：找到下一章节段落[{}]: '{}'", i, text);
                }
            }
        }
        
        // 如果找到了关键段落，应用原有的处理逻辑
        if (liujiangDutyContentIndex != -1) {
            XWPFParagraph lastOrgParagraph = paragraphs.get(liujiangDutyContentIndex);
            setParagraphSpacing(lastOrgParagraph, getParagraphBeforeSpacing(lastOrgParagraph), 0);
            log.info("降级处理：设置柳江区主要职责段落[{}]段后间距为0磅", liujiangDutyContentIndex);
            
            // 如果也找到了下一章节，清理中间的空白段落
            if (workRequirementIndex != -1 && liujiangDutyContentIndex < workRequirementIndex) {
                cleanGapBetweenParagraphs(document, liujiangDutyContentIndex, workRequirementIndex);
                
                // 优化下一章节的间距
                optimizeNextContentSpacing(paragraphs.get(workRequirementIndex));
                log.info("降级处理：已优化下一章节段落[{}]的间距", workRequirementIndex);
            }
        } else {
            // 最后的降级：查找任何可能的组织机构末尾段落
            for (int i = paragraphs.size() - 1; i >= 0; i--) {
                XWPFParagraph paragraph = paragraphs.get(i);
                String text = paragraph.getText();
                
                if (text != null && (
                    text.contains("主要职责") || 
                    text.contains("1、") ||
                    text.contains("应急指挥机构") ||
                    (text.length() > 20 && text.contains("负责"))
                )) {
                    setParagraphSpacing(paragraph, getParagraphBeforeSpacing(paragraph), 0);
                    log.info("最终降级处理：设置段落[{}]段后间距为0磅", i);
                    break;
                }
            }
        }
    }
    
    /**
     * 获取段落的段前间距（磅数）
     */
    private int getParagraphBeforeSpacing(XWPFParagraph paragraph) {
        try {
            CTP ctp = paragraph.getCTP();
            if (ctp != null && ctp.isSetPPr()) {
                CTPPr ppr = ctp.getPPr();
                if (ppr != null && ppr.isSetSpacing()) {
                    CTSpacing spacing = ppr.getSpacing();
                    if (spacing.isSetBefore()) {
                        return spacing.getBefore().intValue() / 20; // 转换为磅
                    }
                }
            }
            return 0;
        } catch (Exception e) {
            log.warn("获取段落段前间距失败: {}", e.getMessage());
            return 0;
        }
    }

    /**
     * 清理组织机构之间的多余空段落
     */
    private void cleanEmptyParagraphsBetweenOrganizations(XWPFDocument document) {
        log.info("=== 开始清理组织机构间的多余空段落 ===");
        
        List<XWPFParagraph> paragraphs = document.getParagraphs();
        List<Integer> emptyParagraphsToRemove = new ArrayList<>();
        
        // 查找需要删除的空段落
        for (int i = 0; i < paragraphs.size(); i++) {
            XWPFParagraph paragraph = paragraphs.get(i);
            String text = paragraph.getText();
            
            // 检查是否是空段落或只包含空白字符的段落
            if (text != null && (text.trim().isEmpty() || text.equals("\n") || text.equals("\r\n"))) {
                // 检查这个空段落是否在组织机构相关内容之间
                if (isBetweenOrganizationContent(paragraphs, i)) {
                    emptyParagraphsToRemove.add(i);
                    log.info("标记删除空段落[{}]: '{}'", i, text.replace("\n", "\\n").replace("\r", "\\r"));
                }
            }
        }
        
        // 从后往前删除空段落，避免索引问题
        for (int i = emptyParagraphsToRemove.size() - 1; i >= 0; i--) {
            int indexToRemove = emptyParagraphsToRemove.get(i);
            try {
                document.removeBodyElement(indexToRemove);
                log.info("成功删除空段落[{}]", indexToRemove);
            } catch (Exception e) {
                log.warn("删除空段落[{}]失败: {}", indexToRemove, e.getMessage());
            }
        }
        
        log.info("空段落清理完成，共删除{}个空段落", emptyParagraphsToRemove.size());
        log.info("=== 组织机构间空段落清理完成 ===");
    }

    /**
     * 判断空段落是否位于组织机构内容之间
     */
    private boolean isBetweenOrganizationContent(List<XWPFParagraph> paragraphs, int emptyIndex) {
        // 检查前后段落是否包含组织机构相关内容
        String previousText = "";
        String nextText = "";
        
        // 获取前一个段落的文本
        if (emptyIndex > 0) {
            XWPFParagraph prevParagraph = paragraphs.get(emptyIndex - 1);
            previousText = prevParagraph.getText();
        }
        
        // 获取后一个段落的文本
        if (emptyIndex < paragraphs.size() - 1) {
            XWPFParagraph nextParagraph = paragraphs.get(emptyIndex + 1);
            nextText = nextParagraph.getText();
        }
        
        // 判断是否在组织机构相关内容之间
        boolean isPrevOrgContent = previousText != null && (
            previousText.contains("主要职责") || 
            previousText.contains("专家组") ||
            previousText.contains("应急指挥机构") ||
            previousText.contains("、") ||
            previousText.contains("（") ||
            previousText.matches(".*\\d+.*") // 包含数字的可能是编号
        );
        
        boolean isNextOrgContent = nextText != null && (
            nextText.contains("应急指挥机构") ||
            nextText.contains("（") ||
            nextText.contains("、") ||
            nextText.matches(".*（[一二三四五六七八九十]+）.*") // 中文编号
        );
        
        return isPrevOrgContent && isNextOrgContent;
    }

    /**
     * 调试：分析专家组和市县级应急指挥机构之间的间距
     */
    private void debugExpertGroupSpacing(XWPFDocument document) {
        log.info("=== 开始分析专家组和市县级应急指挥机构间距 ===");

        List<XWPFParagraph> paragraphs = document.getParagraphs();
        int expertGroupIndex = -1;
        int expertDutyIndex = -1;
        int cityCountyIndex = -1;

        // 查找关键段落的位置
        for (int i = 0; i < paragraphs.size(); i++) {
            XWPFParagraph paragraph = paragraphs.get(i);
            String text = paragraph.getText();

            if (text != null) {
                if (text.contains("专家组")) {
                    expertGroupIndex = i;
                    log.info("找到专家组段落[{}]: '{}'", i, text);
                }

                if (text.contains("主要职责") && expertGroupIndex != -1 && expertDutyIndex == -1) {
                    expertDutyIndex = i;
                    log.info("找到专家组主要职责段落[{}]: '{}'", i, text);
                }

                if (text.contains("市、县级应急指挥机构") || text.contains("县级应急指挥机构")) {
                    cityCountyIndex = i;
                    log.info("找到市、县级应急指挥机构段落[{}]: '{}'", i, text);
                }
            }
        }

        // 分析间距
        if (expertDutyIndex != -1 && cityCountyIndex != -1) {
            log.info("专家组主要职责到市、县级应急指挥机构之间有{}个段落", cityCountyIndex - expertDutyIndex - 1);

            // 打印中间的所有段落及其间距
            for (int i = expertDutyIndex; i <= cityCountyIndex; i++) {
                XWPFParagraph paragraph = paragraphs.get(i);
                String text = paragraph.getText();

                // 获取段落间距信息
                String spacingInfo = getSpacingInfo(paragraph);

                log.info("段落[{}]: '{}' - 间距: {}", i,
                        text != null ? (text.length() > 50 ? text.substring(0, 50) + "..." : text) : "(空)",
                        spacingInfo);
            }
        } else {
            log.warn("未找到专家组主要职责或市、县级应急指挥机构段落");
            log.warn("expertDutyIndex: {}, cityCountyIndex: {}", expertDutyIndex, cityCountyIndex);
        }

        log.info("=== 专家组和市县级应急指挥机构间距分析完成 ===");
    }

    /**
     * 调试：分析文档中所有段落的间距情况
     */
    private void debugAllParagraphSpacing(XWPFDocument document) {
        log.info("=== 开始分析文档中所有段落的间距情况 ===");
        
        List<XWPFParagraph> paragraphs = document.getParagraphs();
        int totalParagraphs = paragraphs.size();
        log.info("文档总段落数: {}", totalParagraphs);
        
        // 查找关键段落并分析其间距
        for (int i = 0; i < paragraphs.size(); i++) {
            XWPFParagraph paragraph = paragraphs.get(i);
            String text = paragraph.getText();
            
            if (text != null && !text.trim().isEmpty()) {
                String spacingInfo = getSpacingInfo(paragraph);
                
                // 重点关注以下类型的段落
                if (text.contains("主要职责") || 
                    text.contains("柳江区应急指挥机构") ||
                    text.contains("专家组") ||
                    text.contains("二、工作要求") ||
                    text.contains("（二）") ||
                    text.contains("市、县级应急指挥机构")) {
                    
                    log.info("*** 关键段落[{}]: '{}' - {}", 
                             i, text.length() > 50 ? text.substring(0, 50) + "..." : text, spacingInfo);
                    
                    // 如果是主要职责标题，查看下一个段落（内容段落）的间距
                    if (text.contains("主要职责") && i + 1 < paragraphs.size()) {
                        XWPFParagraph nextParagraph = paragraphs.get(i + 1);
                        String nextText = nextParagraph.getText();
                        String nextSpacingInfo = getSpacingInfo(nextParagraph);
                        log.info("    ↓ 下一段落[{}]（主要职责内容）: '{}' - {}", 
                                 i + 1, 
                                 nextText != null ? (nextText.length() > 30 ? nextText.substring(0, 30) + "..." : nextText) : "(空)",
                                 nextSpacingInfo);
                        
                        // 特别检查柳江区应急指挥机构的主要职责内容
                        if (nextText != null && (nextText.contains("柳江区") || nextText.contains("柳州市"))) {
                            log.info("    !!! 发现柳江区相关主要职责内容段落，间距信息: {}", nextSpacingInfo);
                        }
                    }
                    
                    // 如果是主要职责内容，特别标注
                    if (i > 0) {
                        XWPFParagraph prevParagraph = paragraphs.get(i - 1);
                        String prevText = prevParagraph.getText();
                        if (prevText != null && prevText.contains("主要职责")) {
                            log.info("    ↑ 这是主要职责内容段落，段后间距应该是关键");
                        }
                    }
                } else if (text.trim().length() < 10) {
                    // 短段落可能是空段落或分隔段落
                    log.debug("段落[{}]: '{}' - {}", i, text.replace("\n", "\\n").replace("\r", "\\r"), spacingInfo);
                }
            }
        }
        
        log.info("=== 文档段落间距分析完成 ===");
    }

    /**
     * 专门调试：分析柳江区应急指挥机构的间距情况
     */
    private void debugLiujiangSpacing(XWPFDocument document) {
        log.info("=== 开始专门分析柳江区应急指挥机构的间距情况 ===");
        
        List<XWPFParagraph> paragraphs = document.getParagraphs();
        int liujiangDeptIndex = -1;
        int liujiangDutyTitleIndex = -1;
        int liujiangDutyContentIndex = -1;
        int workRequirementIndex = -1;
        
        // 查找关键段落的位置
        for (int i = 0; i < paragraphs.size(); i++) {
            XWPFParagraph paragraph = paragraphs.get(i);
            String text = paragraph.getText();
            
            if (text != null) {
                if (text.contains("柳江区应急指挥机构")) {
                    liujiangDeptIndex = i;
                    log.info("找到柳江区应急指挥机构段落[{}]: '{}'", i, text);
                }
                
                if (text.contains("主要职责") && liujiangDeptIndex != -1 && liujiangDutyTitleIndex == -1) {
                    // 确保这是柳江区的主要职责
                    if (i > liujiangDeptIndex && i - liujiangDeptIndex < 5) {
                        liujiangDutyTitleIndex = i;
                        log.info("找到柳江区主要职责标题段落[{}]: '{}'", i, text);
                        
                        // 下一个段落应该是主要职责内容
                        if (i + 1 < paragraphs.size()) {
                            liujiangDutyContentIndex = i + 1;
                            XWPFParagraph contentParagraph = paragraphs.get(i + 1);
                            String contentText = contentParagraph.getText();
                            log.info("找到柳江区主要职责内容段落[{}]: '{}'", 
                                     i + 1, 
                                     contentText != null ? (contentText.length() > 50 ? contentText.substring(0, 50) + "..." : contentText) : "(空)");
                        }
                    }
                }
                
                // 扩大搜索范围查找工作要求段落
                if (text.contains("二、工作要求") || text.contains("工作要求") || 
                    text.matches(".*二[、．].*工作要求.*") || text.matches(".*工作.*要求.*")) {
                    workRequirementIndex = i;
                    log.info("!!! 找到'工作要求'相关段落[{}]: '{}'", i, text);
                }
            }
        }
        
        // 详细分析间距情况
        if (liujiangDutyContentIndex != -1 && workRequirementIndex != -1) {
            log.info("=== 柳江区主要职责内容到'二、工作要求'之间的段落分析 ===");
            log.info("柳江区主要职责内容段落[{}]到'二、工作要求'段落[{}]之间有{}个段落", 
                     liujiangDutyContentIndex, workRequirementIndex, workRequirementIndex - liujiangDutyContentIndex - 1);
            
            // 分析柳江区主要职责内容段落的间距
            XWPFParagraph liujiangDutyContentParagraph = paragraphs.get(liujiangDutyContentIndex);
            String liujiangSpacingInfo = getSpacingInfo(liujiangDutyContentParagraph);
            log.info("!!! 柳江区主要职责内容段落[{}]的间距: {}", liujiangDutyContentIndex, liujiangSpacingInfo);
            
            // 打印中间的所有段落及其间距
            for (int i = liujiangDutyContentIndex; i <= workRequirementIndex; i++) {
                XWPFParagraph paragraph = paragraphs.get(i);
                String text = paragraph.getText();
                String spacingInfo = getSpacingInfo(paragraph);
                
                if (i == liujiangDutyContentIndex) {
                    log.info("*** 段落[{}]（柳江区主要职责内容）: '{}' - 间距: {}", i,
                            text != null ? (text.length() > 50 ? text.substring(0, 50) + "..." : text) : "(空)",
                            spacingInfo);
                } else if (i == workRequirementIndex) {
                    log.info("*** 段落[{}]（二、工作要求）: '{}' - 间距: {}", i,
                            text != null ? text : "(空)",
                            spacingInfo);
                } else {
                    log.info("    段落[{}]: '{}' - 间距: {}", i,
                            text != null ? (text.length() > 30 ? text.substring(0, 30) + "..." : text) : "(空)",
                            spacingInfo);
                }
            }
        } else {
            log.warn("未找到完整的柳江区应急指挥机构结构");
            log.warn("liujiangDutyContentIndex: {}, workRequirementIndex: {}", liujiangDutyContentIndex, workRequirementIndex);
        }
        
        // 直接分析柳江区应急指挥机构段落周围的内容
        if (liujiangDeptIndex >= 0) {
            log.info("=== 详细分析柳江区应急指挥机构段落[{}]周围的内容 ===", liujiangDeptIndex);
            
            // 显示柳江区段落到文档末尾的所有段落
            for (int i = liujiangDeptIndex; i < Math.min(paragraphs.size(), liujiangDeptIndex + 15); i++) {
                XWPFParagraph paragraph = paragraphs.get(i);
                String text = paragraph.getText();
                String spacingInfo = getSpacingInfo(paragraph);
                
                if (i == liujiangDeptIndex) {
                    log.info(">>> 段落[{}]（柳江区应急指挥机构）: '{}' - {}", i, text, spacingInfo);
                } else if (text != null && text.contains("主要职责")) {
                    log.info("!!! 段落[{}]（主要职责标题）: '{}' - {}", i, text, spacingInfo);
                } else if (text != null && text.startsWith("1、全面统筹")) {
                    log.info("*** 段落[{}]（主要职责内容）: '{}' - {}", i, 
                            text.length() > 50 ? text.substring(0, 50) + "..." : text, spacingInfo);
                } else if (text != null && (text.contains("二、") || text.contains("工作要求"))) {
                    log.info("!!! 段落[{}]（工作要求）: '{}' - {}", i, text, spacingInfo);
                } else {
                    log.info("    段落[{}]: '{}' - {}", i, 
                            text != null ? (text.length() > 40 ? text.substring(0, 40) + "..." : text) : "(空)", spacingInfo);
                }
            }
        }
        
        // 如果仍未找到工作要求段落，搜索整个文档
        if (workRequirementIndex == -1) {
            log.warn("未找到'工作要求'段落，搜索包含'二、'的段落...");
            for (int i = 0; i < paragraphs.size(); i++) {
                XWPFParagraph paragraph = paragraphs.get(i);
                String text = paragraph.getText();
                if (text != null && text.contains("二、")) {
                    log.info("发现'二、'段落[{}]: '{}'", i, text);
                }
            }
        }
        
        log.info("=== 柳江区应急指挥机构间距情况分析完成 ===");
    }

    /**
     * 获取段落的间距信息
     */
    private String getSpacingInfo(XWPFParagraph paragraph) {
        try {
            CTP ctp = paragraph.getCTP();
            if (ctp != null && ctp.isSetPPr()) {
                CTPPr ppr = ctp.getPPr();
                if (ppr != null && ppr.isSetSpacing()) {
                    CTSpacing spacing = ppr.getSpacing();

                    String beforeSpacing = spacing.isSetBefore() ?
                        String.valueOf(spacing.getBefore().intValue() / 20) + "磅" : "未设置";
                    String afterSpacing = spacing.isSetAfter() ?
                        String.valueOf(spacing.getAfter().intValue() / 20) + "磅" : "未设置";

                    return String.format("前%s/后%s", beforeSpacing, afterSpacing);
                }
            }
            return "无间距设置";
        } catch (Exception e) {
            return "获取间距失败: " + e.getMessage();
        }
    }

    /**
     * 为Run应用_Style13样式
     * 设置字体为方正仿宋_GB2312，三号，不加粗
     */
    private void applyStyle13ToRun(XWPFRun run) {
        try {
            log.debug("开始应用_Style13样式，设置字体为方正仿宋_GB2312");
            
            // 确保字体样式设置正确
            run.setFontFamily("方正仿宋_GB2312");
            run.setFontSize(16); // 三号字体约16磅
            run.setBold(false); // 确保不加粗
            
                         // 尝试通过OpenXML API应用_Style13样式
             try {
                 CTR ctr = run.getCTR();
                 CTRPr rpr = ctr.isSetRPr() ? ctr.getRPr() : ctr.addNewRPr();
                 
                 // 设置字符样式为_Style13
                 if (rpr.isSetRStyle()) {
                     rpr.getRStyle().setVal("_Style13");
                 } else {
                     CTString rStyle = rpr.addNewRStyle();
                     rStyle.setVal("_Style13");
                 }
                 log.debug("成功通过OpenXML API应用_Style13样式");
             } catch (Exception xmlEx) {
                 log.debug("OpenXML API方式失败，使用基础样式设置: {}", xmlEx.getMessage());
                 // 如果OpenXML方式失败，至少确保基础字体设置正确
                 // 这些设置在前面已经完成了
             }
            
            log.debug("_Style13样式应用完成，字体: 方正仿宋_GB2312, 大小: 16磅(三号), 加粗: false");
        } catch (Exception e) {
            log.warn("应用_Style13样式失败: {}", e.getMessage());
            // 确保基础设置不会丢失
            run.setFontFamily("方正仿宋_GB2312");
            run.setFontSize(16);
            run.setBold(false);
        }
    }

    /**
     * 调试：分析${organization}替换后与emNoticeJobAsk配置信息间的间隙
     */
    private void debugOrganizationToJobAskGap(XWPFDocument document) {
        log.info("🔍🔍🔍 === 开始分析${organization}替换后与emNoticeJobAsk配置信息间的间隙 === 🔍🔍🔍");
        
        List<XWPFParagraph> paragraphs = document.getParagraphs();
        int organizationEndIndex = -1;
        int jobAskStartIndex = -1;
        int liujiangDutyContentIndex = -1;
        
        // 查找关键段落
        for (int i = 0; i < paragraphs.size(); i++) {
            XWPFParagraph paragraph = paragraphs.get(i);
            String text = paragraph.getText();
            
            if (text != null) {
                // 查找柳江区应急指挥机构的主要职责内容段落
                // 改进查找逻辑：寻找包含"柳江区应急指挥机构"后面的主要职责内容
                if (text.contains("柳江区应急指挥机构")) {
                    log.info("🎯 找到柳江区应急指挥机构标题段落[{}]: '{}'", i, text);
                    // 查找后续的主要职责内容段落
                    if (i + 2 < paragraphs.size()) { // 通常主要职责内容在标题后2个段落
                        XWPFParagraph possibleDutyContent = paragraphs.get(i + 2);
                        String dutyText = possibleDutyContent.getText();
                        if (dutyText != null && (dutyText.contains("1、") || dutyText.length() > 20)) {
                            liujiangDutyContentIndex = i + 2;
                            organizationEndIndex = i + 2;
                            log.info("🎯 找到柳江区应急指挥机构主要职责内容段落[{}]: '{}'", i + 2, 
                                    dutyText.length() > 50 ? dutyText.substring(0, 50) + "..." : dutyText);
                        }
                    }
                }
                
                // 查找"二、工作要求"段落
                if (text.contains("二、工作要求")) {
                    jobAskStartIndex = i;
                    log.info("🎯 找到'二、工作要求'段落[{}]: '{}'", i, text);
                }
            }
        }
        
        if (organizationEndIndex != -1 && jobAskStartIndex != -1) {
            log.info("📊 ${organization}部分结束于段落[{}]，emNoticeJobAsk配置信息开始于段落[{}]", 
                     organizationEndIndex, jobAskStartIndex);
            log.info("📊 中间间隔: {}个段落", jobAskStartIndex - organizationEndIndex - 1);
            
            // 详细分析中间的每个段落
            log.info("🔍 详细分析${organization}结束到emNoticeJobAsk开始之间的所有段落:");
            for (int i = organizationEndIndex; i <= jobAskStartIndex; i++) {
                XWPFParagraph paragraph = paragraphs.get(i);
                String text = paragraph.getText();
                String spacingInfo = getSpacingInfo(paragraph);
                String lineSpacingInfo = getLineSpacingInfo(paragraph);
                
                if (i == organizationEndIndex) {
                    log.info("  ⭐ 段落[{}] (${organization}结束): '{}' - 间距: {} - 行距: {}", i,
                            text != null ? (text.length() > 50 ? text.substring(0, 50) + "..." : text) : "(空)",
                            spacingInfo, lineSpacingInfo);
                } else if (i == jobAskStartIndex) {
                    log.info("  ⭐ 段落[{}] (emNoticeJobAsk开始): '{}' - 间距: {} - 行距: {}", i,
                            text != null ? text : "(空)",
                            spacingInfo, lineSpacingInfo);
                } else {
                    log.info("    段落[{}]: '{}' - 间距: {} - 行距: {}", i,
                            text != null ? (text.length() > 30 ? text.substring(0, 30) + "..." : text) : "(空)",
                            spacingInfo, lineSpacingInfo);
                    
                    // 特别标注空白段落
                    if (text == null || text.trim().isEmpty()) {
                        log.info("      ❗ 这是一个空白段落，可能是造成间隙的原因");
                    }
                }
            }
            
            // 建议修复措施
            log.info("🛠️  修复建议:");
            log.info("   1. 柳江区主要职责内容段后间距: 目前已设置为0磅");
            log.info("   2. '二、工作要求'段前间距: 8磅，行距: 1.0倍");
            log.info("   3. 中间空白段落: 需要清理删除");
            
        } else {
            log.warn("❌ 未找到完整的${organization}结束位置或emNoticeJobAsk开始位置");
            log.warn("   organizationEndIndex: {}, jobAskStartIndex: {}", organizationEndIndex, jobAskStartIndex);
            
            // 注意：不再进行紧急修复，标题应该在processSpecialPlaceholders中正确创建
            if (organizationEndIndex != -1 && jobAskStartIndex == -1) {
                log.warn("❌ 仍未找到'二、工作要求'标题！");
                log.warn("💡 这可能表示jobAskContentContainsTitle方法的判断有误，或jobAsk内容处理有问题");
                log.warn("💡 请检查processSpecialPlaceholders方法中的标题创建逻辑");
            }
        }
        
        log.info("🔍🔍🔍 === ${organization}替换后与emNoticeJobAsk配置信息间隙分析完成 === 🔍🔍🔍");
    }

    /**
     * 获取段落的行距信息
     */
    private String getLineSpacingInfo(XWPFParagraph paragraph) {
        try {
            CTP ctp = paragraph.getCTP();
            if (ctp != null && ctp.isSetPPr()) {
                CTPPr ppr = ctp.getPPr();
                if (ppr != null && ppr.isSetSpacing()) {
                    CTSpacing spacing = ppr.getSpacing();
                    
                    if (spacing.isSetLine()) {
                        double lineSpacing = spacing.getLine().doubleValue() / 240.0; // 转换为倍数
                        String lineRule = spacing.isSetLineRule() ? spacing.getLineRule().toString() : "AUTO";
                        return String.format("%.1f倍(%s)", lineSpacing, lineRule);
                    }
                }
            }
            return "默认";
        } catch (Exception e) {
            return "获取失败: " + e.getMessage();
        }
    }

    /**
     * 保存事件企业关联关系
     *
     * @param eventId 事件ID
     * @param enterprisePersonnelIds 企业人员ID列表
     * @param currentUserId 当前用户ID
     */
    private void saveEventEnterpriseRelations(String eventId, List<String> enterprisePersonnelIds, String currentUserId) {
        if (enterprisePersonnelIds == null || enterprisePersonnelIds.isEmpty()) {
            return;
        }

        List<EmergencyEventEnterprise> relationList = new ArrayList<>();
        Long currentTime = System.currentTimeMillis() / 1000;

        for (String enterprisePersonnelId : enterprisePersonnelIds) {
            EmergencyEventEnterprise relation = new EmergencyEventEnterprise();
            relation.setId(UUID.randomUUID().toString());
            relation.setEventId(eventId);
            relation.setEnterprisePersonnelId(enterprisePersonnelId);
            relation.setCreator(currentUserId);
            relation.setUpdater(currentUserId);
            relationList.add(relation);
        }

        if (!relationList.isEmpty()) {
            emergencyEventEnterpriseMapper.batchInsertEmergencyEventEnterprise(relationList);
        }
    }

    /**
     * 更新事件企业关联关系
     *
     * @param eventId 事件ID
     * @param enterprisePersonnelIds 企业人员ID列表
     * @param currentUserId 当前用户ID
     */
    private void updateEventEnterpriseRelations(String eventId, List<String> enterprisePersonnelIds, String currentUserId) {
        // 先删除原有关联关系
        emergencyEventEnterpriseMapper.deleteEmergencyEventEnterpriseByEventId(eventId);

        // 重新保存关联关系
        saveEventEnterpriseRelations(eventId, enterprisePersonnelIds, currentUserId);
    }

}
