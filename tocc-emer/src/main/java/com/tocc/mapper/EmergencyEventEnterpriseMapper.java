package com.tocc.mapper;

import com.tocc.domain.entity.EmergencyEventEnterprise;
import com.tocc.em.domain.EmEnterprisePersonnel;
import java.util.List;

/**
 * 应急事件关联企业Mapper接口
 * 
 * <AUTHOR>
 */
public interface EmergencyEventEnterpriseMapper {
    
    /**
     * 查询应急事件关联企业
     * 
     * @param id 应急事件关联企业主键
     * @return 应急事件关联企业
     */
    EmergencyEventEnterprise selectEmergencyEventEnterpriseById(String id);

    /**
     * 根据事件ID查询关联的企业列表
     * 
     * @param eventId 事件ID
     * @return 企业人员信息集合
     */
    List<EmEnterprisePersonnel> selectEnterpriseListByEventId(String eventId);

    /**
     * 查询应急事件关联企业列表
     * 
     * @param emergencyEventEnterprise 应急事件关联企业
     * @return 应急事件关联企业集合
     */
    List<EmergencyEventEnterprise> selectEmergencyEventEnterpriseList(EmergencyEventEnterprise emergencyEventEnterprise);

    /**
     * 新增应急事件关联企业
     * 
     * @param emergencyEventEnterprise 应急事件关联企业
     * @return 结果
     */
    int insertEmergencyEventEnterprise(EmergencyEventEnterprise emergencyEventEnterprise);

    /**
     * 批量新增应急事件关联企业
     * 
     * @param list 应急事件关联企业集合
     * @return 结果
     */
    int batchInsertEmergencyEventEnterprise(List<EmergencyEventEnterprise> list);

    /**
     * 修改应急事件关联企业
     * 
     * @param emergencyEventEnterprise 应急事件关联企业
     * @return 结果
     */
    int updateEmergencyEventEnterprise(EmergencyEventEnterprise emergencyEventEnterprise);

    /**
     * 删除应急事件关联企业
     * 
     * @param id 应急事件关联企业主键
     * @return 结果
     */
    int deleteEmergencyEventEnterpriseById(String id);

    /**
     * 根据事件ID删除关联企业
     * 
     * @param eventId 事件ID
     * @return 结果
     */
    int deleteEmergencyEventEnterpriseByEventId(String eventId);

    /**
     * 批量删除应急事件关联企业
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteEmergencyEventEnterpriseByIds(String[] ids);
}
