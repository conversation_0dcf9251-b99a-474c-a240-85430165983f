<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tocc.mapper.EmergencyEventEnterpriseMapper">
    
    <resultMap type="com.tocc.domain.entity.EmergencyEventEnterprise" id="EmergencyEventEnterpriseResult">
        <result property="id"                        column="id"                        />
        <result property="eventId"                   column="event_id"                  />
        <result property="enterprisePersonnelId"     column="enterprise_personnel_id"   />
        <result property="createTime"                column="create_time"               />
        <result property="creator"                   column="creator"                   />
        <result property="updateTime"                column="update_time"               />
        <result property="updater"                   column="updater"                   />
    </resultMap>

    <resultMap type="com.tocc.em.domain.EmEnterprisePersonnel" id="EmEnterprisePersonnelResult">
        <result property="enterprisePersonnelId"     column="enterprise_personnel_id"   />
        <result property="enterpriseName"            column="enterprise_name"           />
        <result property="principal"                 column="principal"                 />
        <result property="contactWay"                column="contact_way"               />
        <result property="createTime"                column="create_time"               />
        <result property="creator"                   column="creator"                   />
        <result property="updateTime"                column="update_time"               />
        <result property="updater"                   column="updater"                   />
        <result property="delFlag"                   column="del_flag"                  />
    </resultMap>

    <sql id="selectEmergencyEventEnterpriseVo">
        select id, event_id, enterprise_personnel_id, create_time, creator, update_time, updater 
        from emergency_event_enterprise
    </sql>

    <select id="selectEmergencyEventEnterpriseList" parameterType="com.tocc.domain.entity.EmergencyEventEnterprise" resultMap="EmergencyEventEnterpriseResult">
        <include refid="selectEmergencyEventEnterpriseVo"/>
        <where>  
            <if test="eventId != null  and eventId != ''"> and event_id = #{eventId}</if>
            <if test="enterprisePersonnelId != null  and enterprisePersonnelId != ''"> and enterprise_personnel_id = #{enterprisePersonnelId}</if>
        </where>
    </select>
    
    <select id="selectEmergencyEventEnterpriseById" parameterType="String" resultMap="EmergencyEventEnterpriseResult">
        <include refid="selectEmergencyEventEnterpriseVo"/>
        where id = #{id}
    </select>

    <select id="selectEnterpriseListByEventId" parameterType="String" resultMap="EmEnterprisePersonnelResult">
        select 
            ep.enterprise_personnel_id, ep.enterprise_name, ep.principal, ep.contact_way,
            ep.create_time, ep.creator, ep.update_time, ep.updater, ep.del_flag
        from emergency_event_enterprise eee
        left join em_enterprise_personnel ep on eee.enterprise_personnel_id = ep.enterprise_personnel_id
        where eee.event_id = #{eventId} and ep.del_flag = 0
        order by eee.create_time
    </select>
        
    <insert id="insertEmergencyEventEnterprise" parameterType="com.tocc.domain.entity.EmergencyEventEnterprise">
        insert into emergency_event_enterprise
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="eventId != null and eventId != ''">event_id,</if>
            <if test="enterprisePersonnelId != null and enterprisePersonnelId != ''">enterprise_personnel_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="creator != null">creator,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updater != null">updater,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="eventId != null and eventId != ''">#{eventId},</if>
            <if test="enterprisePersonnelId != null and enterprisePersonnelId != ''">#{enterprisePersonnelId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="creator != null">#{creator},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updater != null">#{updater},</if>
        </trim>
    </insert>

    <insert id="batchInsertEmergencyEventEnterprise" parameterType="java.util.List">
        insert into emergency_event_enterprise (id, event_id, enterprise_personnel_id, create_time, creator, update_time, updater)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.id}, #{item.eventId}, #{item.enterprisePersonnelId}, #{item.createTime}, #{item.creator}, #{item.updateTime}, #{item.updater})
        </foreach>
    </insert>

    <update id="updateEmergencyEventEnterprise" parameterType="com.tocc.domain.entity.EmergencyEventEnterprise">
        update emergency_event_enterprise
        <trim prefix="SET" suffixOverrides=",">
            <if test="eventId != null and eventId != ''">event_id = #{eventId},</if>
            <if test="enterprisePersonnelId != null and enterprisePersonnelId != ''">enterprise_personnel_id = #{enterprisePersonnelId},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updater != null">updater = #{updater},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteEmergencyEventEnterpriseById" parameterType="String">
        delete from emergency_event_enterprise where id = #{id}
    </delete>

    <delete id="deleteEmergencyEventEnterpriseByEventId" parameterType="String">
        delete from emergency_event_enterprise where event_id = #{eventId}
    </delete>

    <delete id="deleteEmergencyEventEnterpriseByIds" parameterType="String">
        delete from emergency_event_enterprise where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
