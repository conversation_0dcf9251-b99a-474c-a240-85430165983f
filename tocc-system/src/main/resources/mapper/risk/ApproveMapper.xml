<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tocc.risk.mapper.ApproveMapper">
    
    <resultMap type="Approve" id="ApproveResult">
        <result property="id"    column="id"    />
        <result property="pitfallsId"    column="pitfalls_id"    />
        <result property="issuedId"    column="issued_id"    />
        <result property="taskId"    column="task_id"    />
        <result property="status"    column="status"    />
        <result property="approveBy"    column="approve_by"    />
        <result property="approveById"    column="approve_by_id"    />
        <result property="approveTime"    column="approve_time"    />
        <result property="step"    column="step"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <resultMap type="java.util.Map" id="approveListResult">
        <result property="id"    column="id"    />
        <result property="city"    column="city"    />
        <result property="district"    column="district"    />
        <result property="units"    column="units"    />
        <result property="inspectType"    column="inspect_type"    />
        <result property="roadNum"    column="road_num"    />
        <result property="riskLevel"    column="risk_level"    />
        <result property="isPitfalls"    column="is_pitfalls"    />
        <result property="remakes"    column="remakes"    />
        <result property="Informant"    column="Informant"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectApproveVo">
        select id, pitfalls_id, issued_id, task_id, status, approve_by, approve_by_id, approve_time, step from risk_approve
    </sql>

    <select id="selectApproveList" resultMap="approveListResult">
        select ra.id, it.id taskId, rp.name, ii.name issuedName, rp.city, rp.district, rp.units, rp.inspect_type, rp.road_num,rp.risk_level,
        rp.is_pitfalls,rp.remakes,it.Informant,it.update_time, ra.step, rp.pile_start, rp.pile_end
        from risk_approve ra
        left join risk_pitfalls rp on ra.pitfalls_id = rp.id
        left join risk_inspect_task it on ra.task_id = it.id
        left join risk_inspect_issued ii on it.issued_id = ii.id
        <where>
            ra.approve_by_id = #{approveById}
            <if test="city != '' and city != null">and rp.city = #{city}</if>
            <if test="district != '' and district != null">and rp.district = #{district}</if>
            <if test="units != '' and units != null">and rp.units = #{units}</if>
            <if test="inspectType != '' and inspectType != null">and rp.inspect_type = #{inspectType}</if>
            <if test="roadNum != '' and roadNum != null">and rp.road_num = #{roadNum}</if>
            <if test="riskLevel != null">and rp.risk_level = #{riskLevel}</if>
            <if test="isPitfalls != null">and rp.is_pitfalls = #{isPitfalls}</if>
            <if test="status != null">and ra.status = #{status}</if>
            <if test="inspectTime != null">and rp.inspect_time = #{inspectTime}</if>
        </where>
        order by ra.create_time desc
    </select>
    
    <select id="selectApproveById" resultMap="ApproveResult">
        <include refid="selectApproveVo"/>
        where id = #{id}
    </select>

    <insert id="insertApprove" parameterType="Approve">
        insert into risk_approve
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="pitfallsId != null">pitfalls_id,</if>
            <if test="issuedId != null">issued_id,</if>
            <if test="taskId != null">task_id,</if>
            <if test="status != null">status,</if>
            <if test="approveBy != null">approve_by,</if>
            <if test="approveById != null">approve_by_id,</if>
            <if test="approveTime != null">approve_time,</if>
            <if test="step != null">step,</if>
            <if test="createTime != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="pitfallsId != null">#{pitfallsId},</if>
            <if test="issuedId != null">#{issuedId},</if>
            <if test="taskId != null">#{taskId},</if>
            <if test="status != null">#{status},</if>
            <if test="approveBy != null">#{approveBy},</if>
            <if test="approveById != null">#{approveById},</if>
            <if test="approveTime != null">#{approveTime},</if>
            <if test="step != null">#{step},</if>
            <if test="createTime != null">#{createTime},</if>
         </trim>
    </insert>

    <update id="updateApprove" parameterType="Approve">
        update risk_approve
        <trim prefix="SET" suffixOverrides=",">
            <if test="pitfallsId != null">pitfalls_id = #{pitfallsId},</if>
            <if test="issuedId != null">issued_id = #{issuedId},</if>
            <if test="taskId != null">task_id = #{taskId},</if>
            <if test="status != null">status = #{status},</if>
            <if test="approveBy != null">approve_by = #{approveBy},</if>
            <if test="approveById != null">approve_by_id = #{approveById},</if>
            <if test="approveTime != null">approve_time = #{approveTime},</if>
            <if test="step != null">step = #{step},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteApproveById" parameterType="Long">
        delete from risk_approve where id = #{id}
    </delete>

    <delete id="deleteApproveByIds" parameterType="String">
        delete from risk_approve where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>