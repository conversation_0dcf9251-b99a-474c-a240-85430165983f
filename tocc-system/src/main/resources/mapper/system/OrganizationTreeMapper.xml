<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tocc.system.mapper.OrganizationTreeMapper">

    <resultMap type="com.tocc.system.domain.vo.OrganizationTreeVO" id="OrganizationTreeResult">
        <result property="id"         column="id"           />
        <result property="deptId"     column="dept_id"      />
        <result property="postId"     column="post_id"      />
        <result property="userId"     column="user_id"      />
        <result property="orgId"      column="org_id"       />
        <result property="name"       column="name"         />
        <result property="type"       column="type"         />
        <result property="parentId"   column="parent_id"    />
        <result property="phone"      column="phone"        />
        <result property="email"      column="email"        />
        <result property="leader"     column="leader"       />
        <result property="nickName"   column="nick_name"    />
        <result property="userName"   column="user_name"    />
        <result property="postCode"   column="post_code"    />
        <result property="unitName"   column="unit_name"    />
        <result property="deptName"   column="dept_name"    />
        <result property="postName"   column="post_name"    />
        <result property="status"     column="status"       />
        <result property="orderNum"   column="order_num"    />
    </resultMap>

    <!-- 查询部门信息 -->
    <select id="selectDeptList" resultMap="OrganizationTreeResult">
        SELECT
            CONCAT(
                CASE
                    WHEN d.org_type = '1' THEN 'unit_'
                    ELSE 'dept_'
                END,
                d.dept_id
            ) as id,
            d.dept_id,
            NULL as post_id,
            NULL as user_id,
            d.dept_name as name,
            CASE
                WHEN d.org_type = '1' THEN 'unit'
                ELSE 'dept'
            END as type,
            CASE
                WHEN d.parent_id = 0 THEN NULL
                ELSE CONCAT(
                    CASE
                        WHEN (SELECT org_type FROM sys_dept WHERE dept_id = d.parent_id) = '1' THEN 'unit_'
                        ELSE 'dept_'
                    END,
                    d.parent_id
                )
            END as parent_id,
            d.phone,
            d.email,
            d.leader,
            NULL as nick_name,
            NULL as user_name,
            NULL as post_code,
            NULL as unit_name,
            NULL as dept_name,
            NULL as post_name,
            d.status,
            d.order_num
        FROM sys_dept d
        WHERE d.del_flag = '0' AND d.status = '0'
        <if test="deptId != null">
            AND d.dept_id = #{deptId}
        </if>
        ORDER BY d.org_type DESC, d.parent_id, d.order_num
    </select>

    <!-- 根据部门ID查询岗位信息 -->
    <select id="selectPostsByDeptId" resultMap="OrganizationTreeResult">
        SELECT DISTINCT
            CONCAT('post_', p.post_id) as id,
            #{deptId} as dept_id,
            p.post_id,
            NULL as user_id,
            p.post_name as name,
            'post' as type,
            CONCAT('dept_', #{deptId}) as parent_id,
            NULL as phone,
            NULL as email,
            NULL as leader,
            NULL as nick_name,
            NULL as user_name,
            p.post_code,
            NULL as unit_name,
            NULL as dept_name,
            NULL as post_name,
            p.status,
            p.post_sort as order_num
        FROM sys_post p
        INNER JOIN sys_user_post up ON p.post_id = up.post_id
        INNER JOIN sys_user u ON up.user_id = u.user_id
        WHERE p.status = '0'
        AND u.del_flag = '0'
        AND u.dept_id = #{deptId}
        ORDER BY p.post_sort
    </select>

    <!-- 根据部门ID和岗位ID查询用户信息 -->
    <select id="selectUsersByDeptAndPost" resultMap="OrganizationTreeResult">
        SELECT
            CONCAT('user_', u.user_id) as id,
            u.dept_id,
            <choose>
                <when test="postId != null">
                    #{postId} as post_id,
                </when>
                <otherwise>
                    NULL as post_id,
                </otherwise>
            </choose>
            u.user_id,
            u.org_id,
            u.nick_name as name,
            'user' as type,
            <choose>
                <when test="postId != null">
                    CONCAT('post_', #{postId}) as parent_id,
                </when>
                <otherwise>
                    CONCAT('dept_', u.dept_id) as parent_id,
                </otherwise>
            </choose>
            u.phonenumber as phone,
            u.email,
            NULL as leader,
            u.nick_name,
            u.user_name,
            NULL as post_code,
            -- 获取单位名称：如果当前部门是单位，则使用当前部门名称；否则查找上级单位
            CASE
                WHEN d.org_type = '1' THEN d.dept_name
                ELSE (
                    SELECT parent_dept.dept_name
                    FROM sys_dept parent_dept
                    WHERE parent_dept.org_type = '1'
                    AND FIND_IN_SET(CAST(parent_dept.dept_id AS CHAR), d.ancestors)
                    ORDER BY parent_dept.dept_id DESC
                    LIMIT 1
                )
            END as unit_name,
            -- 部门名称：如果当前是部门则使用当前名称，如果是单位则为空
            CASE
                WHEN d.org_type = '0' THEN d.dept_name
                ELSE NULL
            END as dept_name,
            -- 岗位名称
            <choose>
                <when test="postId != null">
                    (SELECT post_name FROM sys_post WHERE post_id = #{postId}) as post_name,
                </when>
                <otherwise>
                    NULL as post_name,
                </otherwise>
            </choose>
            u.status,
            NULL as order_num
        FROM sys_user u
        INNER JOIN sys_dept d ON u.dept_id = d.dept_id
        WHERE u.del_flag = '0' AND u.status = '0'
        <if test="deptId != null">
            AND u.dept_id = #{deptId}
        </if>
        <if test="postId != null">
            AND EXISTS (
                SELECT 1 FROM sys_user_post up
                WHERE up.user_id = u.user_id
                AND up.post_id = #{postId}
            )
        </if>
        ORDER BY u.nick_name
    </select>

    <!-- 查询完整的组织架构树形数据 -->
    <select id="selectOrganizationTree" resultMap="OrganizationTreeResult">
        <!-- 部门/单位数据 -->
        SELECT
            CONCAT(
                CASE
                    WHEN d.org_type = '1' THEN 'unit_'
                    WHEN d.org_type = '2' THEN 'enterprise_'
                    ELSE 'dept_'
                END,
                d.dept_id
            ) as id,
            d.dept_id,
            NULL as post_id,
            NULL as user_id,
            NULL as org_id,
            d.dept_name as name,
            CASE
                WHEN d.org_type = '1' THEN 'unit'
                WHEN d.org_type = '2' THEN 'enterprise'
                ELSE 'dept'
            END as type,
            CASE
                WHEN d.parent_id = 0 THEN NULL
                ELSE CONCAT(
                    CASE
                        WHEN (SELECT org_type FROM sys_dept WHERE dept_id = d.parent_id) = '1' THEN 'unit_'
                        WHEN (SELECT org_type FROM sys_dept WHERE dept_id = d.parent_id) = '2' THEN 'enterprise_'
                        ELSE 'dept_'
                    END,
                    d.parent_id
                )
            END as parent_id,
            d.phone,
            d.email,
            d.leader,
            NULL as nick_name,
            NULL as user_name,
            NULL as post_code,
            NULL as unit_name,
            NULL as dept_name,
            NULL as post_name,
            d.status,
            d.order_num
        FROM sys_dept d
        WHERE d.del_flag = '0' AND d.status = '0'
        <if test="deptId != null">
            AND (d.dept_id = #{deptId} OR FIND_IN_SET(CAST(#{deptId} AS CHAR), d.ancestors))
        </if>

        UNION ALL

        <!-- 岗位数据：按部门分组显示岗位 -->
        SELECT DISTINCT
            CONCAT('post_', p.post_id, '_dept_', u.dept_id) as id,
            u.dept_id,
            p.post_id,
            NULL as user_id,
            NULL as org_id,
            p.post_name as name,
            'post' as type,
            CONCAT(
                CASE
                    WHEN d.org_type = '1' THEN 'unit_'
                    WHEN d.org_type = '2' THEN 'enterprise_'
                    ELSE 'dept_'
                END,
                u.dept_id
            ) as parent_id,
            NULL as phone,
            NULL as email,
            NULL as leader,
            NULL as nick_name,
            NULL as user_name,
            p.post_code,
            NULL as unit_name,
            NULL as dept_name,
            NULL as post_name,
            p.status,
            p.post_sort as order_num
        FROM sys_post p
        INNER JOIN sys_user_post up ON p.post_id = up.post_id
        INNER JOIN sys_user u ON up.user_id = u.user_id
        INNER JOIN sys_dept d ON u.dept_id = d.dept_id
        WHERE p.status = '0'
        AND u.del_flag = '0'
        AND u.status = '0'
        AND d.del_flag = '0'
        AND d.status = '0'
        <if test="deptId != null">
            AND (u.dept_id = #{deptId} OR FIND_IN_SET(CAST(#{deptId} AS CHAR), d.ancestors))
        </if>

        UNION ALL

        <!-- 用户数据 -->
        SELECT
            CONCAT('user_', u.user_id) as id,
            u.dept_id,
            up.post_id,
            u.user_id,
            u.org_id,
            u.nick_name as name,
            'user' as type,
            CASE
                WHEN up.post_id IS NOT NULL THEN CONCAT('post_', up.post_id, '_dept_', u.dept_id)
                ELSE CONCAT(
                    CASE
                        WHEN d.org_type = '1' THEN 'unit_'
                        WHEN d.org_type = '2' THEN 'enterprise_'
                        ELSE 'dept_'
                    END,
                    u.dept_id
                )
            END as parent_id,
            u.phonenumber as phone,
            u.email,
            NULL as leader,
            u.nick_name,
            u.user_name,
            NULL as post_code,
            -- 获取单位名称：如果当前部门是单位，则使用当前部门名称；如果是企业，则显示企业名称；否则查找上级单位
            CASE
                WHEN d.org_type = '1' THEN d.dept_name
                WHEN d.org_type = '2' THEN d.dept_name
                ELSE (
                    SELECT parent_dept.dept_name
                    FROM sys_dept parent_dept
                    WHERE parent_dept.org_type IN ('1', '2')
                    AND FIND_IN_SET(CAST(parent_dept.dept_id AS CHAR), d.ancestors)
                    ORDER BY parent_dept.dept_id DESC
                    LIMIT 1
                )
            END as unit_name,
            -- 部门名称：如果当前是部门则使用当前名称，如果是单位则为空
            CASE
                WHEN d.org_type = '0' THEN d.dept_name
                ELSE NULL
            END as dept_name,
            -- 岗位名称
            p.post_name as post_name,
            u.status,
            NULL as order_num
        FROM sys_user u
        LEFT JOIN sys_user_post up ON u.user_id = up.user_id
        LEFT JOIN sys_post p ON up.post_id = p.post_id
        INNER JOIN sys_dept d ON u.dept_id = d.dept_id
        WHERE u.del_flag = '0'
        AND u.status = '0'
        AND d.del_flag = '0'
        AND d.status = '0'
        <if test="deptId != null">
            AND (u.dept_id = #{deptId} OR FIND_IN_SET(CAST(#{deptId} AS CHAR), d.ancestors))
        </if>

        ORDER BY
            CASE type
                WHEN 'unit' THEN 0
                WHEN 'enterprise' THEN 1
                WHEN 'dept' THEN 2
                WHEN 'post' THEN 3
                WHEN 'user' THEN 4
                ELSE 5
            END,
            order_num,
            name
    </select>

</mapper>
