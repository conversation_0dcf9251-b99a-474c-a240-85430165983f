<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tocc.em.mapper.EmEnterprisePersonnelMapper">
    
    <resultMap type="EmEnterprisePersonnel" id="EmEnterprisePersonnelResult">
        <result property="enterprisePersonnelId"    column="enterprise_personnel_id"    />
        <result property="enterpriseName"    column="enterprise_name"    />
        <result property="principal"    column="principal"    />
        <result property="contactWay"    column="contact_way"    />
        <result property="createTime"    column="create_time"    />
        <result property="creator"    column="creator"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updater"    column="updater"    />
        <result property="delFlag"    column="del_flag"    />
    </resultMap>

    <sql id="selectEmEnterprisePersonnelVo">
        select enterprise_personnel_id, enterprise_name, principal, contact_way, create_time, creator, update_time, updater, del_flag from em_enterprise_personnel
    </sql>

    <select id="selectEmEnterprisePersonnelList" parameterType="EmEnterprisePersonnel" resultMap="EmEnterprisePersonnelResult">
        <include refid="selectEmEnterprisePersonnelVo"/>
        <where>
            del_flag = '0'
            <if test="enterpriseName != null  and enterpriseName != ''"> and enterprise_name like concat('%', #{enterpriseName}, '%')</if>
            <if test="principal != null  and principal != ''"> and principal = #{principal}</if>
            <if test="contactWay != null  and contactWay != ''"> and contact_way = #{contactWay}</if>
            <if test="creator != null  and creator != ''"> and creator = #{creator}</if>
            <if test="updater != null  and updater != ''"> and updater = #{updater}</if>
        </where>
    </select>
    
    <select id="selectEmEnterprisePersonnelByEnterprisePersonnelId" parameterType="String" resultMap="EmEnterprisePersonnelResult">
        <include refid="selectEmEnterprisePersonnelVo"/>
        where enterprise_personnel_id = #{enterprisePersonnelId}
    </select>

    <insert id="insertEmEnterprisePersonnel" parameterType="EmEnterprisePersonnel">
        insert into em_enterprise_personnel
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="enterprisePersonnelId != null">enterprise_personnel_id,</if>
            <if test="enterpriseName != null">enterprise_name,</if>
            <if test="principal != null">principal,</if>
            <if test="contactWay != null">contact_way,</if>
            <if test="createTime != null">create_time,</if>
            <if test="creator != null">creator,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updater != null">updater,</if>
            <if test="delFlag != null">del_flag,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="enterprisePersonnelId != null">#{enterprisePersonnelId},</if>
            <if test="enterpriseName != null">#{enterpriseName},</if>
            <if test="principal != null">#{principal},</if>
            <if test="contactWay != null">#{contactWay},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="creator != null">#{creator},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updater != null">#{updater},</if>
            <if test="delFlag != null">#{delFlag},</if>
         </trim>
    </insert>

    <update id="updateEmEnterprisePersonnel" parameterType="EmEnterprisePersonnel">
        update em_enterprise_personnel
        <trim prefix="SET" suffixOverrides=",">
            <if test="enterpriseName != null">enterprise_name = #{enterpriseName},</if>
            <if test="principal != null">principal = #{principal},</if>
            <if test="contactWay != null">contact_way = #{contactWay},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="creator != null">creator = #{creator},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updater != null">updater = #{updater},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
        </trim>
        where enterprise_personnel_id = #{enterprisePersonnelId}
    </update>

    <update id="deleteEmEnterprisePersonnelByEnterprisePersonnelId" parameterType="String">
        update  em_enterprise_personnel set  del_flag = 1 where enterprise_personnel_id = #{enterprisePersonnelId}
    </update>

    <delete id="deleteEmEnterprisePersonnelByEnterprisePersonnelIds" parameterType="String">
        delete from em_enterprise_personnel where enterprise_personnel_id in 
        <foreach item="enterprisePersonnelId" collection="array" open="(" separator="," close=")">
            #{enterprisePersonnelId}
        </foreach>
    </delete>
</mapper>