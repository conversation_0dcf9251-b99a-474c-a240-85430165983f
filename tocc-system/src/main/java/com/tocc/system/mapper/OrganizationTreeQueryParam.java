package com.tocc.system.mapper;

import com.tocc.common.core.domain.BaseEntity;

/**
 * 组织架构树查询参数
 * 
 * <AUTHOR>
 */
public class OrganizationTreeQueryParam extends BaseEntity {
    
    /** 根部门ID，为空时从顶级部门开始 */
    private Long deptId;
    
    public OrganizationTreeQueryParam() {
    }
    
    public OrganizationTreeQueryParam(Long deptId) {
        this.deptId = deptId;
    }
    
    public Long getDeptId() {
        return deptId;
    }
    
    public void setDeptId(Long deptId) {
        this.deptId = deptId;
    }
}
