package com.tocc.system.service;

import com.tocc.system.domain.vo.OrganizationTreeVO;
import java.util.List;

/**
 * 组织架构树形结构 服务层
 * 
 * <AUTHOR>
 */
public interface IOrganizationTreeService {
    
    /**
     * 获取完整的组织架构树形结构
     * 包含部门、岗位、人员信息（名字、联系方式）
     * 
     * @param deptId 根部门ID，为空时从顶级部门开始
     * @return 组织架构树形结构
     */
    List<OrganizationTreeVO> getOrganizationTree(Long deptId);
    
    /**
     * 获取指定部门的组织架构树形结构
     * 
     * @param deptId 部门ID
     * @return 组织架构树形结构
     */
    List<OrganizationTreeVO> getDepartmentTree(Long deptId);
    
    /**
     * 构建树形结构
     * 
     * @param nodes 节点列表
     * @return 树形结构列表
     */
    List<OrganizationTreeVO> buildTree(List<OrganizationTreeVO> nodes);
    
    /**
     * 获取部门下的岗位和人员信息
     *
     * @param deptId 部门ID
     * @return 岗位和人员信息
     */
    List<OrganizationTreeVO> getDepartmentPostsAndUsers(Long deptId);

    /**
     * 获取单位树形结构（包括单位和企业）
     *
     * @param deptId 根单位ID，为空时从顶级单位开始
     * @return 单位树形结构（包括单位和企业）
     */
    List<OrganizationTreeVO> getUnitTree(Long deptId);

    /**
     * 获取部门树形结构（仅部门）
     *
     * @param deptId 根部门ID，为空时从顶级部门开始
     * @return 部门树形结构
     */
    List<OrganizationTreeVO> getDeptTree(Long deptId);

    /**
     * 获取单位-部门层级树形结构
     * 单位作为一级节点，部门作为二级节点
     *
     * @param deptId 根节点ID，为空时从顶级开始
     * @return 单位-部门层级树形结构
     */
    List<OrganizationTreeVO> getUnitDeptTree(Long deptId);
}
