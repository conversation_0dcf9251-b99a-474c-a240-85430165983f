package com.tocc.risk.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.tocc.risk.domain.Pitfalls;
import com.tocc.risk.vo.RiskHome;

import java.util.List;
import java.util.Map;

public interface IRiskHomeService extends IService<RiskHome> {

    /**
     * 隐患点详情
     * @param home
     * @return
     */
    public RiskHome getPitfallsInfo(RiskHome home);

    /**
     * 项目点详情
     * @param home
     * @return
     */
    public RiskHome getProjectInfo(RiskHome home);

    /**
     * 获取风险资源树
     * @return
     */
    public List getResTree();

    /**
     * 获取风险资源树
     * @return
     */
    public Map getReports();

    List<Pitfalls> selectPitfallsList(Pitfalls pitfalls);
}
