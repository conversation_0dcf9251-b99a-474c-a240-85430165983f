package com.tocc.risk.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tocc.common.core.domain.entity.SysDept;
import com.tocc.common.utils.PositionUtil;
import com.tocc.common.utils.SecurityUtils;
import com.tocc.em.domain.EmWarehouse;
import com.tocc.em.mapper.EmWarehouseMapper;
import com.tocc.risk.domain.ModifyTask;
import com.tocc.risk.domain.Pitfalls;
import com.tocc.risk.domain.Projects;
import com.tocc.risk.mapper.ModifyTaskMapper;
import com.tocc.risk.mapper.PitfallsMapper;
import com.tocc.risk.mapper.RiskHomeMapper;
import com.tocc.risk.mapper.RiskProjectsMapper;
import com.tocc.risk.service.IRiskHomeService;
import com.tocc.risk.vo.MaterialVO;
import com.tocc.risk.vo.RescueTeamVO;
import com.tocc.risk.vo.RiskHome;
import com.tocc.system.mapper.SysDeptMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.*;

@Service
public class RiskHomeServiceImpl extends ServiceImpl<RiskHomeMapper, RiskHome> implements IRiskHomeService {


    @Autowired
    private RiskProjectsMapper projectsMapper;
    @Autowired
    private PitfallsMapper pitfallsMapper;
    @Autowired
    private ModifyTaskMapper modifyTaskMapper;
    @Autowired
    private EmWarehouseMapper warehouseMapper;
    @Autowired
    private SysDeptMapper deptMapper;


    /**
     * 隐患点详情
     * @param home
     * @return
     */
    @Override
    public RiskHome getPitfallsInfo(RiskHome home) {
        // 获取隐患点数据
        Pitfalls pitfalls = pitfallsMapper.selectPitfallsById(home.getId());
        // 获取隐患点的整改数据
        ModifyTask task = modifyTaskMapper.selectByPitfallsId(pitfalls.getId());
        // 获取应急物资
        List<EmWarehouse> warehouses = warehouseMapper.selectEmWarehouseList(null);
        // 距离计算，获取最近一个应急物资点
        EmWarehouse em = getPit(warehouses, pitfalls);
        List<MaterialVO> materialList = baseMapper.getMaterialByWarId(em.getId());
        // 获取救援点
        List<RescueTeamVO> teamList = baseMapper.selectRescueTeamList();
        // 距离计算，获取最近一个救援点
        RescueTeamVO te = getRe(teamList, pitfalls);
        List<MaterialVO> materialTeamList = baseMapper.getMaterialByTeamId(te.getId());

        home.setPitfalls(pitfalls);
        home.setModifyTask(task);
        home.setWarehouse(em);
        home.setRescueTeam(te);
        home.setMaterialList(materialList);
        home.setMaterialTeamList(materialTeamList);

        return home;
    }

    private EmWarehouse getPit(List<EmWarehouse> warehouses, Pitfalls p) {
        for (EmWarehouse w : warehouses) {
            double log1 = Double.parseDouble(w.getLongitude());
            double lat1 = Double.parseDouble(w.getLatitude());
            double log2 = Double.parseDouble(p.getLot());
            double lat2 = Double.parseDouble(p.getLat());
            double distance = PositionUtil.getDistance1(log1, lat1, log2, lat2) / 1000;
            BigDecimal bd = new BigDecimal(distance);
            bd = bd.setScale(2, RoundingMode.HALF_UP);
            w.setDistance(bd.doubleValue());
        }
        Optional<EmWarehouse> nearest = warehouses.stream()
                .min(Comparator.comparingDouble(EmWarehouse::getDistance));
        return nearest.orElse(null);
    }

    private RescueTeamVO getRe(List<RescueTeamVO> teamList, Pitfalls p) {
        for (RescueTeamVO r : teamList) {
            double log1 = r.getLongitude().doubleValue();
            double lat1 = r.getLatitude().doubleValue();
            double log2 = Double.parseDouble(p.getLot());
            double lat2 = Double.parseDouble(p.getLat());
            double distance = PositionUtil.getDistance2(log1, lat1, log2, lat2) / 1000;
            BigDecimal bd = new BigDecimal(distance);
            bd = bd.setScale(2, RoundingMode.HALF_UP);
            r.setDistance(bd.doubleValue());
        }
        Optional<RescueTeamVO> nearest = teamList.stream()
                .min(Comparator.comparingDouble(RescueTeamVO::getDistance));
        return nearest.orElse(null);
    }

    /**
     * 项目点详情
     * @param home
     * @return
     */
    @Override
    public RiskHome getProjectInfo(RiskHome home) {
        // 获取项目数据
        Projects projects = projectsMapper.selectRiskProjectsById(home.getId());
        // 获取项目隐患列表数据
        Pitfalls p = new Pitfalls();
        p.setProjectId(projects.getId());
        List<Pitfalls> pitfallsList = pitfallsMapper.selectPitfallsList(p);
        //
        // 获取应急物资
        List<EmWarehouse> warehouses = warehouseMapper.selectEmWarehouseList(null);
        // 距离计算，获取最近一个应急物资点
        EmWarehouse em = getProjectPit(warehouses, projects);
        List<MaterialVO> materialList = baseMapper.getMaterialByWarId(em.getId());
        // 获取救援点
        List<RescueTeamVO> teamList = baseMapper.selectRescueTeamList();
        // 距离计算，获取最近一个救援点
        RescueTeamVO te = getProjectRe(teamList, projects);
        List<MaterialVO> materialTeamList = baseMapper.getMaterialByTeamId(te.getId());


        home.setProjects(projects);
        home.setPitfallsList(pitfallsList);
        home.setWarehouse(em);
        home.setRescueTeam(te);
        home.setMaterialList(materialList);
        home.setMaterialTeamList(materialTeamList);

        return home;
    }

    /**
     * 获取风险资源树
     * @return
     */
    @Override
    public List getResTree() {
        List<Map<String, Object>> parents = baseMapper.getIssuedList();
        for (Map<String, Object> parent : parents) {
            // 获取任务下的领域
            String issuedId = parent.get("value").toString();
            List<Map<String, Object>> areas = baseMapper.getAreasList(issuedId);
            for (Map<String, Object> area : areas) {
                String val = parent.get("value").toString()+ "-" + area.get("value").toString();
                area.put("value",  val);
                Map<String, String> map = new HashMap<>();
                map.put("label", "自然灾害风险");
                map.put("value", val + "-1");
                Map<String, String> map2 = new HashMap<>();
                map2.put("label", "人为风险");
                map2.put("value", val + "-2");
                List<Map<String, String>> list = new ArrayList<>();
                list.add(map);
                list.add(map2);
                area.put("children", list);
            }
            parent.put("children", areas);
        }
        return parents;
    }

    @Override
    public Map getReports() {
        SysDept dept = deptMapper.selectDeptById(SecurityUtils.getOrgId());
        // 获取风险隐患统计
        Map map = baseMapper.getPitfallReprots(dept.getAncestors());

        Map ma = baseMapper.getProjectReprots(dept.getAncestors());
        map.put("allPro", ma.get("allPro"));
        return map;
    }

    @Override
    public List<Pitfalls> selectPitfallsList(Pitfalls pitfalls) {


        pitfalls.setIsApprove(1);
        return baseMapper.selectPitfallsList(pitfalls);
    }


    private EmWarehouse getProjectPit(List<EmWarehouse> warehouses, Projects p) {
        for (EmWarehouse w : warehouses) {
            double log1 = Double.parseDouble(w.getLongitude());
            double lat1 = Double.parseDouble(w.getLatitude());
            String[] coordinate = p.getCoordinate().split(",");
            double log2 = Double.parseDouble(coordinate[0]);
            double lat2 = Double.parseDouble(coordinate[1]);
            double distance = PositionUtil.getDistance1(log1, lat1, log2, lat2);
            w.setDistance(distance);
        }
        Optional<EmWarehouse> nearest = warehouses.stream()
                .min(Comparator.comparingDouble(EmWarehouse::getDistance));
        return nearest.orElse(null);
    }

    private RescueTeamVO getProjectRe(List<RescueTeamVO> teamList, Projects p) {
        for (RescueTeamVO r : teamList) {
            double log1 = r.getLongitude().doubleValue();
            double lat1 = r.getLatitude().doubleValue();
            String[] coordinate = p.getCoordinate().split(",");
            double log2 = Double.parseDouble(coordinate[0]);
            double lat2 = Double.parseDouble(coordinate[1]);
            double distance = PositionUtil.getDistance1(log1, lat1, log2, lat2);
            r.setDistance(distance);
        }
        Optional<RescueTeamVO> nearest = teamList.stream()
                .min(Comparator.comparingDouble(RescueTeamVO::getDistance));
        return nearest.orElse(null);
    }

}
