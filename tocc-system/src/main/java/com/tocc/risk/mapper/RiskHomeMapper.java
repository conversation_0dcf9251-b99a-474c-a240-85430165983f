package com.tocc.risk.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tocc.risk.domain.Pitfalls;
import com.tocc.risk.vo.MaterialVO;
import com.tocc.risk.vo.RescueTeamVO;
import com.tocc.risk.vo.RiskHome;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;


@Mapper
public interface RiskHomeMapper extends BaseMapper<RiskHome>
{
    List<MaterialVO> getMaterialByWarId(String id);
    List<MaterialVO> getMaterialByTeamId(String id);

    List<RescueTeamVO> selectRescueTeamList();

    List<Map<String, Object>> getIssuedList();

    List<Map<String, Object>> getAreasList(String issuedId);

    Map getPitfallReprots(String anc);

    Map getProjectReprots(String anc);

    List<Pitfalls> selectPitfallsList(Pitfalls pitfalls);
}
