package com.tocc.em.service.impl;

import java.util.List;

import com.tocc.common.core.domain.model.LoginUser;
import com.tocc.common.utils.DateUtils;
import com.tocc.common.utils.SecurityUtils;
import com.tocc.common.utils.uuid.IdUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.tocc.em.mapper.EmEnterprisePersonnelMapper;
import com.tocc.em.domain.EmEnterprisePersonnel;
import com.tocc.em.service.IEmEnterprisePersonnelService;

/**
 * 企业人员信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-18
 */
@Service
public class EmEnterprisePersonnelServiceImpl implements IEmEnterprisePersonnelService 
{
    @Autowired
    private EmEnterprisePersonnelMapper emEnterprisePersonnelMapper;

    /**
     * 查询企业人员信息
     * 
     * @param enterprisePersonnelId 企业人员信息主键
     * @return 企业人员信息
     */
    @Override
    public EmEnterprisePersonnel selectEmEnterprisePersonnelByEnterprisePersonnelId(String enterprisePersonnelId)
    {
        return emEnterprisePersonnelMapper.selectEmEnterprisePersonnelByEnterprisePersonnelId(enterprisePersonnelId);
    }

    /**
     * 查询企业人员信息列表
     * 
     * @param emEnterprisePersonnel 企业人员信息
     * @return 企业人员信息
     */
    @Override
    public List<EmEnterprisePersonnel> selectEmEnterprisePersonnelList(EmEnterprisePersonnel emEnterprisePersonnel)
    {
        return emEnterprisePersonnelMapper.selectEmEnterprisePersonnelList(emEnterprisePersonnel);
    }

    /**
     * 新增企业人员信息
     * 
     * @param emEnterprisePersonnel 企业人员信息
     * @return 结果
     */
    @Override
    public int insertEmEnterprisePersonnel(EmEnterprisePersonnel emEnterprisePersonnel)
    {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        String userName = loginUser.getUser().getUserName();
        emEnterprisePersonnel.setCreator(userName);
        emEnterprisePersonnel.setUpdater(userName);
        emEnterprisePersonnel.setEnterprisePersonnelId(IdUtils.fastSimpleUUID());
        return emEnterprisePersonnelMapper.insertEmEnterprisePersonnel(emEnterprisePersonnel);
    }

    /**
     * 修改企业人员信息
     * 
     * @param emEnterprisePersonnel 企业人员信息
     * @return 结果
     */
    @Override
    public int updateEmEnterprisePersonnel(EmEnterprisePersonnel emEnterprisePersonnel)
    {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        String userName = loginUser.getUser().getUserName();
        emEnterprisePersonnel.setUpdater(userName);
        return emEnterprisePersonnelMapper.updateEmEnterprisePersonnel(emEnterprisePersonnel);
    }

    /**
     * 批量删除企业人员信息
     * 
     * @param enterprisePersonnelIds 需要删除的企业人员信息主键
     * @return 结果
     */
    @Override
    public int deleteEmEnterprisePersonnelByEnterprisePersonnelIds(String[] enterprisePersonnelIds)
    {
        for (String enterprisePersonnelId : enterprisePersonnelIds) {
            emEnterprisePersonnelMapper.deleteEmEnterprisePersonnelByEnterprisePersonnelId(enterprisePersonnelId);
        }
        return enterprisePersonnelIds.length;
    }

    /**
     * 删除企业人员信息信息
     * 
     * @param enterprisePersonnelId 企业人员信息主键
     * @return 结果
     */
    @Override
    public int deleteEmEnterprisePersonnelByEnterprisePersonnelId(String enterprisePersonnelId)
    {
        return emEnterprisePersonnelMapper.deleteEmEnterprisePersonnelByEnterprisePersonnelId(enterprisePersonnelId);
    }
}
