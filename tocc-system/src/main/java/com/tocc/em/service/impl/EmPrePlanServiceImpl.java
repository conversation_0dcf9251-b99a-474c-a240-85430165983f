package com.tocc.em.service.impl;

import java.time.LocalDateTime;
import java.util.*;
import java.util.Date;

import com.tocc.common.core.domain.model.LoginUser;
import com.tocc.common.utils.SecurityUtils;
import com.tocc.common.utils.bean.BeanUtils;
import com.tocc.common.utils.uuid.IdUtils;
import com.tocc.em.domain.*;
import com.tocc.em.dto.*;
import com.tocc.em.enums.EmPrePlanStatus;
import com.tocc.em.qo.EmEventLevelQO;
import com.tocc.em.qo.EmPrePlanQO;
import com.tocc.em.service.*;
import com.tocc.em.domain.EmPrePlanDeptUser;
import com.tocc.em.vo.EmPrePlanVO;
import com.tocc.em.vo.EmPrePlanVersionVO;
import com.tocc.em.vo.EmPrePlanCompareVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.tocc.em.mapper.EmPrePlanMapper;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

/**
 * 应急预案数据Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-31
 */
@Service
public class EmPrePlanServiceImpl implements IEmPrePlanService
{
    @Autowired
    private EmPrePlanMapper emPrePlanMapper;

    @Resource
    private IEmPrePlanDeptService emPrePlanDeptService ;

    @Resource
    private IEmEventLevelService emEventLevelService ;

    @Resource
    private IEmMeasureService measureService ;

    @Resource
    private IEmPrePlanFileService fileService ;

    @Resource
    private IEmPrePlanRecordService prePlanRecordService ;

    @Resource 
    private IEmPrePlanDeptUserService iEmPrePlanDeptUserService;

    /**
     * 查询应急预案数据
     *
     * @param id 应急预案数据主键
     * @return 应急预案数据
     */
    @Override
    public EmPrePlanVO selectEmPrePlanById(String id)
    {   EmPrePlanVO emPrePlanVO = new EmPrePlanVO();
        EmPrePlan emPrePlan = emPrePlanMapper.selectEmPrePlanById(id);
        BeanUtils.copyProperties(emPrePlan,emPrePlanVO);
        String version = emPrePlan.getVersion();
        
        // 先查询应急组织体系，后面响应单位查询需要使用
        EmPrePlanDept emPrePlanDept = new EmPrePlanDept();
        emPrePlanDept.setVersion(version);
        emPrePlanDept.setPrePlanId(id);
        List<EmPrePlanDeptDTO> allDeptList = emPrePlanDeptService.selectEmPrePlanDeptListWithTree(emPrePlanDept);
        // 将树形结构扁平化，便于后续查询
        List<EmPrePlanDeptDTO> flatDeptList = flattenDeptTree(allDeptList);
        
        // 事件分级与响应
        EmEventLevelQO emEventLevelQO = new EmEventLevelQO();
        emEventLevelQO.setVersion(version);
        emEventLevelQO.setPrePlanId(id);
        List<EmEventLevel> emEventLevels = emEventLevelService.selectEmEventLevelList(emEventLevelQO);
        List<EmEventLevelDTO> levelDTOList = new ArrayList<>();
        for (EmEventLevel emEventLevel : emEventLevels) {
            EmEventLevelDTO emEventLevelDTO = new EmEventLevelDTO();
            BeanUtils.copyProperties(emEventLevel,emEventLevelDTO);
            // 手动设置ID，因为Entity中id是String类型，而DTO中id是Long类型，BeanUtils无法自动转换
            if (emEventLevel.getId() != null) {
                try {
                    emEventLevelDTO.setId(emEventLevel.getId());
                } catch (NumberFormatException e) {
                    // 如果ID不是数字格式，保持为null
                    emEventLevelDTO.setId(null);
                }
            }
            
            // 响应单位：根据事件级别查询对应的应急组织机构
            List<EmPrePlanDeptDTO> responseDepts = new ArrayList<>();
            if (emEventLevel.getEventLevel() != null && flatDeptList != null) {
                String currentEventLevel = emEventLevel.getEventLevel();
                for (EmPrePlanDeptDTO dept : flatDeptList) {
                    // 比较事件级别，支持多等级交集判断
                    if (hasEventLevelIntersection(currentEventLevel, dept.getEventLevel())) {
                        responseDepts.add(dept);
                    }
                }
                //  将扁平化的响应单位重新构建为树形结构
                responseDepts = EmPrePlanDeptServiceImpl.buildTree(responseDepts);
            }
            emEventLevelDTO.setEmPrePlanDeptDTOList(responseDepts);
            
            levelDTOList.add(emEventLevelDTO);
        }
        emPrePlanVO.setLevelDTOList( levelDTOList );
        
        // 应急组织体系（使用之前查询的数据）
        emPrePlanVO.setEmPrePlanDeptDTOList(allDeptList);
        
        //处置措施
        EmMeasure emMeasure = new EmMeasure();
        emMeasure.setVersion(version);
        emMeasure.setPrePlanId(id);
        List<EmMeasure> emMeasures = measureService.selectEmMeasureList(emMeasure);
        List<EmMeasureDTO> measureDTOList = new ArrayList<>();
        for (EmMeasure measure : emMeasures) {
            EmMeasureDTO emMeasureDTO = new EmMeasureDTO();
            BeanUtils.copyProperties(measure,emMeasureDTO);
            // 手动设置ID，因为Entity中id是String类型，而DTO中id是Long类型，BeanUtils无法自动转换
            if (measure.getId() != null) {
                try {
                    emMeasureDTO.setId(Long.valueOf(measure.getId()));
                } catch (NumberFormatException e) {
                    // 如果ID不是数字格式，保持为null
                    emMeasureDTO.setId(null);
                }
            }
            measureDTOList.add(emMeasureDTO);
        }
        emPrePlanVO.setEmMeasureDTOList(measureDTOList);
        // 附件
        EmPrePlanFile emPrePlanFile = new EmPrePlanFile();
        emPrePlanFile.setVersion(version);
        emPrePlanFile.setBizId(id);
        List<EmPrePlanFile> emPrePlanFiles = fileService.selectEmPrePlanFileList(emPrePlanFile);
        List<EmPrePlanFileDTO> fileDTOList = new ArrayList<>();
        for (EmPrePlanFile prePlanFile : emPrePlanFiles) {
            EmPrePlanFileDTO emPrePlanFileDTO = new EmPrePlanFileDTO();
            BeanUtils.copyProperties(prePlanFile,emPrePlanFileDTO);
            fileDTOList.add(emPrePlanFileDTO);
        }
        emPrePlanVO.setEmPrePlanFileDTOS(fileDTOList);
        return emPrePlanVO;
    }

    /**
     * 查询应急预案数据列表
     *
     * @param emPrePlanQO 应急预案数据
     * @return 应急预案数据
     */
    @Override
    public List<EmPrePlan> selectEmPrePlanList(EmPrePlanQO emPrePlanQO)
    {     EmPrePlan emPrePlan = new EmPrePlan();
        //1我的预案，2厅级预案，3市级预案，4直属单位预案，5草稿箱
//        if("1".equals(emPrePlanQO.getQueryType())){
//            String userName = SecurityUtils.getLoginUser().getUser().getUserName();
//            emPrePlanQO.setCreator(userName);
//        }
//        if("2".equals(emPrePlanQO.getQueryType())){
//            emPrePlanQO.setDeptType("1");
//        }
//        if("3".equals(emPrePlanQO.getQueryType())){
//            emPrePlanQO.setDeptType("3");
//        }
//        if("4".equals(emPrePlanQO.getQueryType())){
//            emPrePlanQO.setDeptType("2");
//        }
//        if("5".equals(emPrePlanQO.getQueryType())){
//            emPrePlanQO.setPlanStatus(0);
//        }else {
//            emPrePlanQO.setPlanStatus(1);
//        }
//        //本周检状态 检查状态(0未检查/1已检查)
//        if(null != emPrePlanQO.getCheckStatus()){
//            Date weekStart = DateUtil.beginOfWeek(new Date());
//            Date weekEnd = DateUtil.endOfWeek(new Date());
//            Map<String ,Object> param = new HashMap<>();
//            param.put("weekStart",DateUtil.format(weekStart, "yyyy-MM-dd HH:mm:ss"));
//            param.put("weekEnd",DateUtil.format(weekEnd, "yyyy-MM-dd HH:mm:ss"));
//            emPrePlanQO.setParams(param);
//        }
        return emPrePlanMapper.selectEmPrePlanList(emPrePlanQO);
    }

    /**
     * 新增应急预案数据
     *
     * @param emPrePlanDTO 应急预案数据
     * @return 结果
     */
    @Override
    @Transactional
    public int insertEmPrePlan( EmPrePlanDTO emPrePlanDTO)
    {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        String username = loginUser.getUsername();
        EmPrePlan emPrePlan = new EmPrePlan();
        BeanUtils.copyProperties(emPrePlanDTO,emPrePlan);
        String emPrePlanID = IdUtils.fastSimpleUUID();
        emPrePlan.setId(emPrePlanID);
        emPrePlan.setVersion("0.1");
        emPrePlan.setCreator(username);
        emPrePlan.setUpdateBy(username);
        emPrePlanMapper.insertEmPrePlan(emPrePlan);

        // 事件分级与响应
        List<EmEventLevelDTO> levelDTOList =emPrePlanDTO.getLevelDTOList();
        List<EmEventLevel> emEventLevels = new ArrayList<>();
        for (EmEventLevelDTO emEventLevelDTO : levelDTOList) {
            EmEventLevel  eventLevel = new EmEventLevel();
            BeanUtils.copyProperties(emEventLevelDTO,eventLevel);
            eventLevel.setId(IdUtils.fastSimpleUUID());
            eventLevel.setPrePlanId(emPrePlanID);
            eventLevel.setCreator(username);
            eventLevel.setUpdater(username);
            eventLevel.setUpdateBy(username);
            eventLevel.setVersion(emPrePlan.getVersion());
            eventLevel.setVersion(emPrePlan.getVersion());
            emEventLevels.add(eventLevel);
        }
        if(!emEventLevels.isEmpty()){
            emEventLevelService.insertBatchEmEventLevel(emEventLevels);
        }
        // 应急组织体系
        List<EmPrePlanDeptDTO> emPrePlanDeptDTOList =emPrePlanDTO.getEmPrePlanDeptDTOList() ;
        if(null != emPrePlanDeptDTOList && !emPrePlanDeptDTOList.isEmpty()){
            emPrePlanDeptService.saveEmPrePlanDept(emPrePlan.getVersion(),emPrePlanID,emPrePlanDeptDTOList);
        }
        //处置措施
        List<EmMeasureDTO> emMeasureDTOList = emPrePlanDTO.getEmMeasureDTOList() ;
        if(null != emMeasureDTOList && !emMeasureDTOList.isEmpty()){
            for (EmMeasureDTO emMeasureDTO : emMeasureDTOList) {
                EmMeasure measure = new EmMeasure();
                BeanUtils.copyProperties(emMeasureDTO,measure);
                measure.setPrePlanId(emPrePlanID);
                measure.setCreator(username);
                measure.setUpdateBy(username);
                measure.setVersion(emPrePlan.getVersion());
                measure.setId(IdUtils.fastSimpleUUID());
                measureService.insertEmMeasure(measure);
            }
        }
        // 附件
        List<EmPrePlanFileDTO> emPrePlanFileDTOS = emPrePlanDTO.getEmPrePlanFileDTOS();
        if(null != emPrePlanFileDTOS && !emPrePlanFileDTOS.isEmpty()){
            for (EmPrePlanFileDTO emPrePlanFileDTO : emPrePlanFileDTOS) {
                EmPrePlanFile emPrePlanFile = new EmPrePlanFile();
                BeanUtils.copyProperties(emPrePlanFileDTO,emPrePlanFile);
                emPrePlanFile.setBizId(emPrePlanID);
                emPrePlanFile.setCreator(username);
                emPrePlanFile.setUpdateBy(username);
                emPrePlanFile.setVersion(emPrePlan.getVersion());
                emPrePlanFile.setId(IdUtils.fastSimpleUUID());
                fileService.insertEmPrePlanFile(emPrePlanFile);
            }
        }

        return  1;
    }

    /**
     * 修改应急预案数据
     *
     * @param emPrePlanDTO 应急预案数据
     * @return 结果
     */
    @Override
    public int updateEmPrePlan(EmPrePlanDTO emPrePlanDTO)
    {
        String userName = SecurityUtils.getLoginUser().getUser().getUserName();

        //查询更新前状态
        String prePlanId = emPrePlanDTO.getId();
        EmPrePlan emPrePlanOld = emPrePlanMapper.selectEmPrePlanById(prePlanId);
        Integer planStatusOld = emPrePlanOld.getPlanStatus();

        if(EmPrePlanStatus.SUBMITTED.getValue().equals(planStatusOld)){
            //新增版本并留痕
            addEmPrePlanVersion(emPrePlanDTO);
        }else{
            //直接更新
            EmPrePlan emPrePlan = new EmPrePlan();
            BeanUtils.copyProperties(emPrePlanDTO,emPrePlan);
            //清除 关联 start--- 使用旧记录的版本号清除关联数据
            clearAssociated(emPrePlanDTO, emPrePlanOld);
            //清除 关联 end---

            // 新增关联 start --
            addAssociated(emPrePlanDTO, userName);
            // 新增关联 end --
            emPrePlanMapper.updateEmPrePlan(emPrePlan);
        }
        return  1;
    }

    private void addAssociated(EmPrePlanDTO emPrePlanDTO, String userName) {
        String id =emPrePlanDTO.getId();
        // 事件分级与响应
        List<EmEventLevelDTO> levelDTOList = emPrePlanDTO.getLevelDTOList();
        List<EmEventLevel> emEventLevels = new ArrayList<>();
        for (EmEventLevelDTO emEventLevelDTO : levelDTOList) {
            EmEventLevel  eventLevel = new EmEventLevel();
            BeanUtils.copyProperties(emEventLevelDTO,eventLevel);
            eventLevel.setId(IdUtils.fastSimpleUUID());
            eventLevel.setPrePlanId(id);
            eventLevel.setCreator(userName);
            eventLevel.setUpdater(userName);
            eventLevel.setUpdateBy(userName);
            eventLevel.setVersion(emPrePlanDTO.getVersion());
            emEventLevels.add(eventLevel);
        }
        emEventLevelService.insertBatchEmEventLevel(emEventLevels);
        // 应急组织体系
        List<EmPrePlanDeptDTO> emPrePlanDeptDTOList = emPrePlanDTO.getEmPrePlanDeptDTOList() ;
        if(null != emPrePlanDeptDTOList && !emPrePlanDeptDTOList.isEmpty()){
            emPrePlanDeptService.saveEmPrePlanDept(emPrePlanDTO.getVersion(), id,emPrePlanDeptDTOList);
        }
        //处置措施
        List<EmMeasureDTO> emMeasureDTOList = emPrePlanDTO.getEmMeasureDTOList() ;
        if(null != emMeasureDTOList && !emMeasureDTOList.isEmpty()){
            for (EmMeasureDTO emMeasureDTO : emMeasureDTOList) {
                EmMeasure measure = new EmMeasure();
                BeanUtils.copyProperties(emMeasureDTO,measure);
                measure.setPrePlanId(id);
                measure.setCreator(userName);
                measure.setUpdateBy(userName);
                measure.setVersion(emPrePlanDTO.getVersion());
                measure.setId(IdUtils.fastSimpleUUID());
                measureService.insertEmMeasure(measure);
            }
        }
        // 附件
        List<EmPrePlanFileDTO> emPrePlanFileDTOS = emPrePlanDTO.getEmPrePlanFileDTOS();
        if(null != emPrePlanFileDTOS && !emPrePlanFileDTOS.isEmpty()){
            for (EmPrePlanFileDTO emPrePlanFileDTO : emPrePlanFileDTOS) {
                EmPrePlanFile emPrePlanFile = new EmPrePlanFile();
                BeanUtils.copyProperties(emPrePlanFileDTO,emPrePlanFile);
                emPrePlanFile.setBizId(id);
                emPrePlanFile.setCreator(userName);
                emPrePlanFile.setUpdateBy(userName);
                emPrePlanFile.setVersion(emPrePlanDTO.getVersion());
                emPrePlanFile.setId(IdUtils.fastSimpleUUID());
                fileService.insertEmPrePlanFile(emPrePlanFile);
            }
        }
    }

    private String clearAssociated(EmPrePlanDTO emPrePlanDTO, EmPrePlan emPrePlan) {
        String version = emPrePlan.getVersion();
        String id = emPrePlanDTO.getId();
        // 事件分级与响应
        EmEventLevelQO emEventLevelQO = new EmEventLevelQO();
        emEventLevelQO.setVersion(version);
        emEventLevelQO.setPrePlanId(id);
        List<EmEventLevel> emEventLevels = emEventLevelService.selectEmEventLevelList(emEventLevelQO);
        for (EmEventLevel emEventLevel : emEventLevels) {
            emEventLevelService.deleteEmEventLevelById(emEventLevel.getId());
        }
        // 应急组织体系 - 修复树形结构处理问题
        EmPrePlanDept emPrePlanDept = new EmPrePlanDept();
        emPrePlanDept.setVersion(version);
        emPrePlanDept.setPrePlanId(id);
        List<EmPrePlanDeptDTO> treeList = emPrePlanDeptService.selectEmPrePlanDeptListWithTree(emPrePlanDept);
        
        // 将树形结构扁平化，确保处理所有节点
        List<EmPrePlanDeptDTO> flatList = flattenDeptTree(treeList);
        
        // 先删除所有机构下的人员（避免外键约束问题）
        for (EmPrePlanDeptDTO deptDTO : flatList) {
            EmPrePlanDeptUser deptUserQuery = new EmPrePlanDeptUser();
            deptUserQuery.setEmDeptId(deptDTO.getId());
            List<EmPrePlanDeptUser> deptUsers = iEmPrePlanDeptUserService.selectEmPrePlanDeptUserList(deptUserQuery);
            for (EmPrePlanDeptUser deptUser : deptUsers) {
                iEmPrePlanDeptUserService.deleteEmPrePlanDeptUserById(deptUser.getId());
            }
        }
        
        // 再删除所有机构（从子级到父级，避免外键约束问题）
        for (int i = flatList.size() - 1; i >= 0; i--) {
            emPrePlanDeptService.deleteEmPrePlanDeptById(flatList.get(i).getId());
        }
        
        //处置措施
        EmMeasure emMeasure = new EmMeasure();
        emMeasure.setVersion(version);
        emMeasure.setPrePlanId(id);
        List<EmMeasure> emMeasures = measureService.selectEmMeasureList(emMeasure);
        for (EmMeasure measure : emMeasures) {
            measureService.deleteEmMeasureById(measure.getId());
        }
        // 附件
        EmPrePlanFile emPrePlanFile = new EmPrePlanFile();
        emPrePlanFile.setVersion(version);
        emPrePlanFile.setBizId(id);
        List<EmPrePlanFile> emPrePlanFiles = fileService.selectEmPrePlanFileList(emPrePlanFile);
        for (EmPrePlanFile prePlanFile : emPrePlanFiles) {
            fileService.deleteEmPrePlanFileById(prePlanFile.getId());
        }
        return id;
    }

    /**
     * 将树形部门结构扁平化为列表
     * 
     * @param treeList 树形部门列表
     * @return 扁平化的部门列表
     */
    private List<EmPrePlanDeptDTO> flattenDeptTree(List<EmPrePlanDeptDTO> treeList) {
        List<EmPrePlanDeptDTO> flatList = new ArrayList<>();
        if (treeList != null && !treeList.isEmpty()) {
            for (EmPrePlanDeptDTO dept : treeList) {
                addDeptToFlatList(dept, flatList);
            }
        }
        return flatList;
    }

    /**
     * 递归将部门及其子部门添加到扁平列表中
     * 
     * @param dept 部门节点
     * @param flatList 扁平列表
     */
    private void addDeptToFlatList(EmPrePlanDeptDTO dept, List<EmPrePlanDeptDTO> flatList) {
        // 添加当前部门
        flatList.add(dept);
        
        // 递归处理子部门
        if (dept.getChildren() != null && !dept.getChildren().isEmpty()) {
            for (EmPrePlanDeptDTO child : dept.getChildren()) {
                addDeptToFlatList(child, flatList);
            }
        }
    }

    private void addEmPrePlanVersion(EmPrePlanDTO emPrePlanDTO) {
        //修订内容必填校验
        if (emPrePlanDTO.getRevisionContent() == null || emPrePlanDTO.getRevisionContent().isEmpty()) {
           throw  new RuntimeException("修订内容未录入！！");
        }
        String userName = SecurityUtils.getLoginUser().getUser().getUserName();
        //留痕
        EmPrePlanRecord emPrePlanRecord = new EmPrePlanRecord();
        BeanUtils.copyProperties(emPrePlanDTO,emPrePlanRecord);
        emPrePlanRecord.setPrePlanId(emPrePlanDTO.getId());
        emPrePlanRecord.setId(IdUtils.fastSimpleUUID());
        prePlanRecordService.insertEmPrePlanRecord(emPrePlanRecord);


        //增加版本号
        String version = emPrePlanDTO.getVersion();
        String[] parts = version.split("\\.");
        int minor = Integer.parseInt(parts[1]) + 1;
        String  newVersion=  parts[0] + "." + minor;
        emPrePlanDTO.setVersion(newVersion);
        emPrePlanDTO.setReviser(userName);
        emPrePlanDTO.setRevisionTime(new Date());
        EmPrePlan emPrePlanNewVersion = new EmPrePlan();
        BeanUtils.copyProperties(emPrePlanDTO,emPrePlanNewVersion);
        emPrePlanMapper.updateEmPrePlan(emPrePlanNewVersion);

        addAssociated(emPrePlanDTO,userName);
    }

    /**
     * 批量删除应急预案数据
     *
     * @param ids 需要删除的应急预案数据主键
     * @return 结果
     */
    @Override
    public int deleteEmPrePlanByIds(String[] ids)
    {
        return emPrePlanMapper.deleteEmPrePlanByIds(ids);
    }

    /**
     * 删除应急预案数据信息
     *
     * @param id 应急预案数据主键
     * @return 结果
     */
    @Override
    public int deleteEmPrePlanById(String id)
    {
        return emPrePlanMapper.deleteEmPrePlanById(id);
    }

    /**
     * 查询更新超时的应急预案
     *
     * @param timeoutTime 超时时间点
     * @return 超时的应急预案集合
     */
    @Override
    public List<EmPrePlanVO> selectTimeoutPlans(LocalDateTime timeoutTime) {
        return emPrePlanMapper.selectTimeoutPlans(timeoutTime);
    }

    /**
     * 查询预案的所有版本（包括当前版本和历史版本）
     *
     * @param prePlanId 预案ID，如果为null则查询所有预案的版本
     * @return 版本列表，按照版本号降序排列
     */
    @Override
    public List<EmPrePlanVersionVO> selectEmPrePlanVersionHistory(String prePlanId) {
        return emPrePlanMapper.selectEmPrePlanVersionHistory(prePlanId);
    }

    /**
     * 根据预案名称查询预案的所有版本（包括当前版本和历史版本）
     *
     * @param planName 预案名称
     * @return 版本列表，按照版本号降序排列
     */
    @Override
    public List<EmPrePlanVersionVO> selectEmPrePlanVersionHistoryByName(String planName) {
        return emPrePlanMapper.selectEmPrePlanVersionHistoryByName(planName);
    }

    /**
     * 根据预案ID和版本号查询预案详情
     *
     * @param prePlanId 预案ID
     * @param version 版本号
     * @return 预案详情
     */
    @Override
    public EmPrePlanVO selectEmPrePlanByIdAndVersion(String prePlanId, String version) {
        if (prePlanId == null || prePlanId.trim().isEmpty()) {
            throw new IllegalArgumentException("预案ID不能为空");
        }
        if (version == null || version.trim().isEmpty()) {
            throw new IllegalArgumentException("版本号不能为空");
        }
        
        EmPrePlanVO result = emPrePlanMapper.selectEmPrePlanByIdAndVersion(prePlanId, version);
        if (result == null) {
            throw new RuntimeException("未找到指定ID[" + prePlanId + "]和版本号[" + version + "]的预案");
        }
        
        // 查询关联信息
        loadAssociatedData(result, prePlanId, version);
        
        return result;
    }

    /**
     * 加载预案的关联数据
     *
     * @param emPrePlanVO 预案VO对象
     * @param prePlanId 预案ID
     * @param version 版本号
     */
    private void loadAssociatedData(EmPrePlanVO emPrePlanVO, String prePlanId, String version) {
        // 先查询应急组织体系，后面响应单位查询需要使用
        EmPrePlanDept emPrePlanDept = new EmPrePlanDept();
        emPrePlanDept.setVersion(version);
        emPrePlanDept.setPrePlanId(prePlanId);
        List<EmPrePlanDeptDTO> allDeptList = emPrePlanDeptService.selectEmPrePlanDeptListWithTree(emPrePlanDept);
        // 将树形结构扁平化，便于后续查询
        List<EmPrePlanDeptDTO> flatDeptList = flattenDeptTree(allDeptList);
        
        // 事件分级与响应
        EmEventLevelQO emEventLevelQO = new EmEventLevelQO();
        emEventLevelQO.setVersion(version);
        emEventLevelQO.setPrePlanId(prePlanId);
        List<EmEventLevel> emEventLevels = emEventLevelService.selectEmEventLevelList(emEventLevelQO);
        List<EmEventLevelDTO> levelDTOList = new ArrayList<>();
        for (EmEventLevel emEventLevel : emEventLevels) {
            EmEventLevelDTO emEventLevelDTO = new EmEventLevelDTO();
            BeanUtils.copyProperties(emEventLevel, emEventLevelDTO);
            // 手动设置ID，因为Entity中id是String类型，而DTO中id是Long类型，BeanUtils无法自动转换
            if (emEventLevel.getId() != null) {
                try {
                    emEventLevelDTO.setId(emEventLevel.getId());
                } catch (NumberFormatException e) {
                    // 如果ID不是数字格式，保持为null
                    emEventLevelDTO.setId(null);
                }
            }
            
            // 响应单位：根据事件级别查询对应的应急组织机构
            List<EmPrePlanDeptDTO> responseDepts = new ArrayList<>();
            if (emEventLevel.getEventLevel() != null && flatDeptList != null) {
                String currentEventLevel = String.valueOf(emEventLevel.getEventLevel());
                for (EmPrePlanDeptDTO dept : flatDeptList) {
                    // 比较事件级别，支持多等级交集判断
                    if (hasEventLevelIntersection(currentEventLevel, dept.getEventLevel())) {
                        responseDepts.add(dept);
                    }
                }
                // 将扁平化的响应单位重新构建为树形结构
                responseDepts = EmPrePlanDeptServiceImpl.buildTree(responseDepts);
            }
            emEventLevelDTO.setEmPrePlanDeptDTOList(responseDepts);
            
            levelDTOList.add(emEventLevelDTO);
        }
        emPrePlanVO.setLevelDTOList(levelDTOList);

        // 应急组织体系（使用之前查询的数据）
        emPrePlanVO.setEmPrePlanDeptDTOList(allDeptList);

        // 处置措施
        EmMeasure emMeasure = new EmMeasure();
        emMeasure.setVersion(version);
        emMeasure.setPrePlanId(prePlanId);
        List<EmMeasure> emMeasures = measureService.selectEmMeasureList(emMeasure);
        List<EmMeasureDTO> measureDTOList = new ArrayList<>();
        for (EmMeasure measure : emMeasures) {
            EmMeasureDTO emMeasureDTO = new EmMeasureDTO();
            BeanUtils.copyProperties(measure, emMeasureDTO);
            // 手动设置ID，因为Entity中id是String类型，而DTO中id是Long类型，BeanUtils无法自动转换
            if (measure.getId() != null) {
                try {
                    emMeasureDTO.setId(Long.valueOf(measure.getId()));
                } catch (NumberFormatException e) {
                    // 如果ID不是数字格式，保持为null
                    emMeasureDTO.setId(null);
                }
            }
            measureDTOList.add(emMeasureDTO);
        }
        emPrePlanVO.setEmMeasureDTOList(measureDTOList);

        // 附件
        EmPrePlanFile emPrePlanFile = new EmPrePlanFile();
        emPrePlanFile.setVersion(version);
        emPrePlanFile.setBizId(prePlanId);
        List<EmPrePlanFile> emPrePlanFiles = fileService.selectEmPrePlanFileList(emPrePlanFile);
        List<EmPrePlanFileDTO> fileDTOList = new ArrayList<>();
        for (EmPrePlanFile prePlanFile : emPrePlanFiles) {
            EmPrePlanFileDTO emPrePlanFileDTO = new EmPrePlanFileDTO();
            BeanUtils.copyProperties(prePlanFile, emPrePlanFileDTO);
            fileDTOList.add(emPrePlanFileDTO);
        }
        emPrePlanVO.setEmPrePlanFileDTOS(fileDTOList);
    }

    /**
     * 比较当前版本与历史版本的预案内容
     *
     * @param prePlanId 预案ID
     * @param historyVersion 历史版本号
     * @return 比较结果
     */
    @Override
    public EmPrePlanCompareVO compareVersions(String prePlanId, String historyVersion) {
        if (prePlanId == null || prePlanId.trim().isEmpty()) {
            throw new IllegalArgumentException("预案ID不能为空");
        }
        if (historyVersion == null || historyVersion.trim().isEmpty()) {
            throw new IllegalArgumentException("历史版本号不能为空");
        }

        // 获取该预案的所有版本历史，按版本号降序排列
        List<EmPrePlanVersionVO> versionHistory = selectEmPrePlanVersionHistory(prePlanId);
        if (versionHistory == null || versionHistory.isEmpty()) {
            throw new IllegalArgumentException("未找到该预案的版本信息");
        }

        // 获取最新版本（第一个就是最新版本，因为按version DESC排序）
        EmPrePlanVersionVO latestVersionInfo = versionHistory.get(0);
        String latestVersion = latestVersionInfo.getVersion();

        // 判断：预案ID必须是最新的（当前版本必须是最新版本）
        EmPrePlanVO currentVersion = selectEmPrePlanById(prePlanId);
        if (!latestVersion.equals(currentVersion.getVersion())) {
            throw new IllegalArgumentException("只能比较最新版本的预案，当前预案版本(" + currentVersion.getVersion() + ")不是最新版本(" + latestVersion + ")");
        }

        // 判断：版本号参数值不能是最新的
        if (latestVersion.equals(historyVersion)) {
            throw new IllegalArgumentException("比较的版本号(" + historyVersion + ")不能是最新版本，请选择其他历史版本进行比较");
        }

        // 获取历史版本数据
        EmPrePlanVO historyVersionData = selectEmPrePlanByIdAndVersion(prePlanId, historyVersion);

        // 创建比较结果
        EmPrePlanCompareVO compareResult = new EmPrePlanCompareVO();
        compareResult.setPrePlanId(prePlanId);
        compareResult.setPlanName(currentVersion.getPlanName());
        compareResult.setCurrentVersion(currentVersion.getVersion());
        compareResult.setHistoryVersion(historyVersion);

        // 比较基础信息
        compareResult.setBasicDifferences(compareBasicFields(currentVersion, historyVersionData));

        // 比较关联数据
        compareResult.setLevelDifferences(compareEventLevels(currentVersion.getLevelDTOList(), historyVersionData.getLevelDTOList()));
        compareResult.setDeptDifferences(compareDepts(currentVersion.getEmPrePlanDeptDTOList(), historyVersionData.getEmPrePlanDeptDTOList()));
        compareResult.setMeasureDifferences(compareMeasures(currentVersion.getEmMeasureDTOList(), historyVersionData.getEmMeasureDTOList()));
        // 添加回附件比较
        compareResult.setFileDifferences(compareFiles(currentVersion.getEmPrePlanFileDTOS(), historyVersionData.getEmPrePlanFileDTOS()));

        return compareResult;
    }

    /**
     * 根据历史版本ID比较历史版本与最新版本的预案内容
     */
    @Override
    public EmPrePlanCompareVO compareVersionsByHistoryId(String historyVersionId) {
        if (historyVersionId == null || historyVersionId.trim().isEmpty()) {
            throw new IllegalArgumentException("历史版本ID不能为空");
        }

        // 查询历史版本记录
        EmPrePlanRecord historyRecord = prePlanRecordService.selectEmPrePlanRecordById(historyVersionId);
        if (historyRecord == null) {
            throw new IllegalArgumentException("未找到指定的历史版本记录");
        }

        // 获取预案ID和历史版本号
        String prePlanId = historyRecord.getPrePlanId();
        String historyVersion = historyRecord.getVersion();

        // 获取该预案的所有版本历史，找到最新版本
        List<EmPrePlanVersionVO> versionHistory = selectEmPrePlanVersionHistory(prePlanId);
        if (versionHistory == null || versionHistory.isEmpty()) {
            throw new IllegalArgumentException("未找到该预案的版本信息");
        }

        // 获取最新版本（第一个就是最新版本，因为按version DESC排序）
        EmPrePlanVersionVO latestVersionInfo = versionHistory.get(0);
        String latestVersion = latestVersionInfo.getVersion();

        // 检查历史版本不能是最新版本
        if (latestVersion.equals(historyVersion)) {
            throw new IllegalArgumentException("该版本(" + historyVersion + ")是最新版本，无法与自己比较");
        }

        // 获取最新版本数据
        EmPrePlanVO currentVersion = selectEmPrePlanById(prePlanId);
        
        // 获取历史版本数据
        EmPrePlanVO historyVersionData = selectEmPrePlanByIdAndVersion(prePlanId, historyVersion);

        // 创建比较结果
        EmPrePlanCompareVO compareResult = new EmPrePlanCompareVO();
        compareResult.setPrePlanId(prePlanId);
        compareResult.setPlanName(currentVersion.getPlanName());
        compareResult.setCurrentVersion(latestVersion);  // 当前显示的是最新版本
        compareResult.setHistoryVersion(historyVersion);

        // 比较基础信息
        compareResult.setBasicDifferences(compareBasicFields(currentVersion, historyVersionData));

        // 比较关联数据
        compareResult.setLevelDifferences(compareEventLevels(currentVersion.getLevelDTOList(), historyVersionData.getLevelDTOList()));
        compareResult.setDeptDifferences(compareDepts(currentVersion.getEmPrePlanDeptDTOList(), historyVersionData.getEmPrePlanDeptDTOList()));
        compareResult.setMeasureDifferences(compareMeasures(currentVersion.getEmMeasureDTOList(), historyVersionData.getEmMeasureDTOList()));
        compareResult.setFileDifferences(compareFiles(currentVersion.getEmPrePlanFileDTOS(), historyVersionData.getEmPrePlanFileDTOS()));

        return compareResult;
    }

    /**
     * 检查指定的历史版本ID是否为最新版本
     */
    @Override
    public boolean isLatestVersion(String historyVersionId) {
        if (historyVersionId == null || historyVersionId.trim().isEmpty()) {
            return false;
        }

        try {
            // 查询历史版本记录
            EmPrePlanRecord historyRecord = prePlanRecordService.selectEmPrePlanRecordById(historyVersionId);
            if (historyRecord == null) {
                return false;
            }

            // 获取预案ID和历史版本号
            String prePlanId = historyRecord.getPrePlanId();
            String historyVersion = historyRecord.getVersion();

            // 获取该预案的所有版本历史，找到最新版本
            List<EmPrePlanVersionVO> versionHistory = selectEmPrePlanVersionHistory(prePlanId);
            if (versionHistory == null || versionHistory.isEmpty()) {
                return false;
            }

            // 获取最新版本（第一个就是最新版本，因为按version DESC排序）
            EmPrePlanVersionVO latestVersionInfo = versionHistory.get(0);
            String latestVersion = latestVersionInfo.getVersion();

            // 检查历史版本是否是最新版本
            return latestVersion.equals(historyVersion);
        } catch (Exception e) {
            // 如果出现异常，默认返回false
            return false;
        }
    }

    /**
     * 比较基础字段信息
     */
    private List<EmPrePlanCompareVO.FieldDifference> compareBasicFields(EmPrePlanVO current, EmPrePlanVO history) {
        List<EmPrePlanCompareVO.FieldDifference> differences = new ArrayList<>();
        
        // 预案名称
        addFieldDifference(differences, "planName", "预案名称", current.getPlanName(), history.getPlanName());
        
        // 预案类型
        addFieldDifference(differences, "planType", "预案类型", current.getPlanType(), history.getPlanType());
        
        // 编制单位
        addFieldDifference(differences, "compilingDept", "编制单位", current.getCompilingDept(), history.getCompilingDept());
        
        // 适用单位
        addFieldDifference(differences, "applicableDeptIds", "适用单位", current.getApplicableDeptIds(), history.getApplicableDeptIds());
        
        // 编制目的
        addFieldDifference(differences, "purpose", "编制目的", current.getPurpose(), history.getPurpose());
        
        // 编制依据
        addFieldDifference(differences, "basis", "编制依据", current.getBasis(), history.getBasis());
        
        // 适用范围
        addFieldDifference(differences, "scope", "适用范围", current.getScope(), history.getScope());
        
        // 工作原则
        addFieldDifference(differences, "workPrinciple", "工作原则", current.getWorkPrinciple(), history.getWorkPrinciple());
        
        // 预防措施
        addFieldDifference(differences, "preventiveMeasures", "预防措施", current.getPreventiveMeasures(), history.getPreventiveMeasures());
        
        // 预警原则
        addFieldDifference(differences, "warningPrinciple", "预警原则", current.getWarningPrinciple(), history.getWarningPrinciple());
        
        // 预警信息收集
        addFieldDifference(differences, "warningInfoCollect", "预警信息收集", current.getWarningInfoCollect(), history.getWarningInfoCollect());
        
        // 预警分级
        addFieldDifference(differences, "warningLevel", "预警分级", current.getWarningLevel(), history.getWarningLevel());
        
        // 预警发布
        addFieldDifference(differences, "warningPublish", "预警发布", current.getWarningPublish(), history.getWarningPublish());
        
        // 预警措施
        addFieldDifference(differences, "warningMeasures", "预警措施", current.getWarningMeasures(), history.getWarningMeasures());
        
        // 事件级别
        addFieldDifference(differences, "eventLevel", "事件级别", current.getEventLevel(), history.getEventLevel());
        
        // 响应启动条件
        addFieldDifference(differences, "responseCondition", "响应启动条件", current.getResponseCondition(), history.getResponseCondition());
        
        // 应急处置流程
        addFieldDifference(differences, "processFlow", "应急处置流程", current.getProcessFlow(), history.getProcessFlow());
        
        // 信息报送
        addFieldDifference(differences, "infoReport", "信息报送", current.getInfoReport(), history.getInfoReport());
        
        // 新闻发布
        addFieldDifference(differences, "newsRelease", "新闻发布", current.getNewsRelease(), history.getNewsRelease());
        
        // 响应调整与终止
        addFieldDifference(differences, "responseAdjust", "响应调整与终止", current.getResponseAdjust(), history.getResponseAdjust());
        
        // 善后处置
        addFieldDifference(differences, "aftermathDisposal", "善后处置", current.getAftermathDisposal(), history.getAftermathDisposal());
        
        // 总结评估
        addFieldDifference(differences, "summaryEvaluation", "总结评估", current.getSummaryEvaluation(), history.getSummaryEvaluation());
        
        // 物资保障
        addFieldDifference(differences, "materialSupport", "物资保障", current.getMaterialSupport(), history.getMaterialSupport());
        
        // 通信保障
        addFieldDifference(differences, "communicationSupport", "通信保障", current.getCommunicationSupport(), history.getCommunicationSupport());
        
        // 交通保障
        addFieldDifference(differences, "trafficSupport", "交通保障", current.getTrafficSupport(), history.getTrafficSupport());
        
        // 经费保障
        addFieldDifference(differences, "fundingSupport", "经费保障", current.getFundingSupport(), history.getFundingSupport());
        
        // 预案修订
        addFieldDifference(differences, "planRevision", "预案修订", current.getPlanRevision(), history.getPlanRevision());
        
        // 宣传培训
        addFieldDifference(differences, "publicityTraining", "宣传培训", current.getPublicityTraining(), history.getPublicityTraining());
        
        // 预案演练
        addFieldDifference(differences, "planDrill", "预案演练", current.getPlanDrill(), history.getPlanDrill());
        
        // 实施时间
        addFieldDifference(differences, "implementTime", "实施时间", current.getImplementTime(), history.getImplementTime());
        
        // 预案状态
        addFieldDifference(differences, "planStatus", "预案状态", 
                current.getPlanStatus() != null ? String.valueOf(current.getPlanStatus()) : "", 
                history.getPlanStatus() != null ? String.valueOf(history.getPlanStatus()) : "");
        
        // 启用状态
        addFieldDifference(differences, "enableStatus", "启用状态", 
                current.getEnableStatus() != null ? String.valueOf(current.getEnableStatus()) : "", 
                history.getEnableStatus() != null ? String.valueOf(history.getEnableStatus()) : "");
        
        // 检查状态
        addFieldDifference(differences, "checkStatus", "检查状态", 
                current.getCheckStatus() != null ? String.valueOf(current.getCheckStatus()) : "", 
                history.getCheckStatus() != null ? String.valueOf(history.getCheckStatus()) : "");
        
        // 最新检查时间
        addFieldDifference(differences, "lastCheckTime", "最新检查时间", 
                formatDate(current.getLastCheckTime()), 
                formatDate(history.getLastCheckTime()));
        
        // 创建人
        addFieldDifference(differences, "creator", "创建人", current.getCreator(), history.getCreator());
        
        // 创建时间
        addFieldDifference(differences, "createTime", "创建时间", 
                formatDate(current.getCreateTime()), 
                formatDate(history.getCreateTime()));
        
        // 更新人
        addFieldDifference(differences, "updater", "更新人", current.getUpdater(), history.getUpdater());
        
        // 更新时间
        addFieldDifference(differences, "updateTime", "更新时间", 
                formatDate(current.getUpdateTime()), 
                formatDate(history.getUpdateTime()));
        
        // 修订人
        addFieldDifference(differences, "reviser", "修订人", current.getReviser(), history.getReviser());
        
        // 修订时间
        addFieldDifference(differences, "revisionTime", "修订时间", 
                formatDate(current.getRevisionTime()), 
                formatDate(history.getRevisionTime()));
        
        // 修订内容
        addFieldDifference(differences, "revisionContent", "修订内容", current.getRevisionContent(), history.getRevisionContent());
        
        return differences;
    }

    /**
     * 添加字段差异
     */
    private void addFieldDifference(List<EmPrePlanCompareVO.FieldDifference> differences, 
                                   String fieldName, String fieldLabel, String currentValue, String historyValue) {
        EmPrePlanCompareVO.FieldDifference diff = new EmPrePlanCompareVO.FieldDifference();
        diff.setFieldName(fieldName);
        diff.setFieldLabel(fieldLabel);
        diff.setCurrentValue(currentValue == null ? "" : currentValue);
        diff.setHistoryValue(historyValue == null ? "" : historyValue);
        diff.setIsDifferent(!Objects.equals(currentValue, historyValue));
        differences.add(diff);
    }

    /**
     * 格式化日期为字符串
     */
    private String formatDate(Date date) {
        if (date == null) {
            return "";
        }
        try {
            java.text.SimpleDateFormat sdf = new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            return sdf.format(date);
        } catch (Exception e) {
            return date.toString();
        }
    }

    /**
     * 比较事件分级与响应
     */
    private List<EmPrePlanCompareVO.AssociatedDataDifference> compareEventLevels(
            List<EmEventLevelDTO> currentList, List<EmEventLevelDTO> historyList) {
        return compareAssociatedData("事件分级与响应", currentList, historyList, 
                level -> level.getId() != null ? String.valueOf(level.getId()) : "", 
                level -> {
                    // 基于事件级别和条件生成唯一标识符
                    String eventLevel = level.getEventLevel() != null ? String.valueOf(level.getEventLevel()) : "";
                    String conditions = level.getConditions() != null ? level.getConditions().trim() : "";
                    return eventLevel + "||" + conditions; // 使用双竖线作为分隔符避免冲突
                });
    }

    /**
     * 比较应急组织体系（树形结构）
     */
    private List<EmPrePlanCompareVO.AssociatedDataDifference> compareDepts(
            List<EmPrePlanDeptDTO> currentList, List<EmPrePlanDeptDTO> historyList) {
        
        // 扁平化树形结构并生成部门路径
        List<DeptWithPath> currentFlat = flattenDeptTreeWithPath(currentList, "");
        List<DeptWithPath> historyFlat = flattenDeptTreeWithPath(historyList, "");
        
        List<EmPrePlanCompareVO.AssociatedDataDifference> differences = new ArrayList<>();
        Map<String, DeptWithPath> currentMap = new HashMap<>();
        Map<String, DeptWithPath> historyMap = new HashMap<>();
        
        // 构建映射（基于完整路径）
        for (DeptWithPath item : currentFlat) {
            currentMap.put(item.getFullPath(), item);
        }
        for (DeptWithPath item : historyFlat) {
            historyMap.put(item.getFullPath(), item);
        }
        
        // 找出所有唯一路径
        Set<String> allPaths = new HashSet<>();
        allPaths.addAll(currentMap.keySet());
        allPaths.addAll(historyMap.keySet());
        
        // 比较每个路径对应的部门
        for (String path : allPaths) {
            DeptWithPath currentItem = currentMap.get(path);
            DeptWithPath historyItem = historyMap.get(path);
            
            EmPrePlanCompareVO.AssociatedDataDifference diff = new EmPrePlanCompareVO.AssociatedDataDifference();
            diff.setDataType("应急组织体系");
            diff.setDataId(path);
            diff.setDataIdentifier(path);
            
            if (currentItem != null && historyItem != null) {
                // 两个版本都存在，比较详细内容（包括用户数据）
                boolean isContentSame = compareDeptDetailContent(currentItem.getDept(), historyItem.getDept());
                if (!isContentSame) {
                    diff.setOperationType("MODIFIED");
                    diff.setCurrentData(currentItem.getDept());
                    diff.setHistoryData(historyItem.getDept());
                    differences.add(diff);
                }
            } else if (currentItem != null) {
                // 只在当前版本存在，表示新增
                diff.setOperationType("ADDED");
                diff.setCurrentData(currentItem.getDept());
                diff.setHistoryData(null);
                differences.add(diff);
            } else {
                // 只在历史版本存在，表示删除
                diff.setOperationType("DELETED");
                diff.setCurrentData(null);
                diff.setHistoryData(historyItem.getDept());
                differences.add(diff);
            }
        }
        
        return differences;
    }

    /**
     * 扁平化部门树形结构并生成完整路径
     */
    private List<DeptWithPath> flattenDeptTreeWithPath(List<EmPrePlanDeptDTO> treeList, String parentPath) {
        List<DeptWithPath> result = new ArrayList<>();
        if (treeList == null || treeList.isEmpty()) {
            return result;
        }
        
        for (EmPrePlanDeptDTO dept : treeList) {
            // 生成当前部门的完整路径
            String currentPath = parentPath.isEmpty() ? 
                dept.getDeptName() : 
                parentPath + " > " + dept.getDeptName();
            
            // 添加当前部门
            result.add(new DeptWithPath(dept, currentPath));
            
            // 递归处理子部门
            if (dept.getChildren() != null && !dept.getChildren().isEmpty()) {
                result.addAll(flattenDeptTreeWithPath(dept.getChildren(), currentPath));
            }
        }
        
        return result;
    }

    /**
     * 比较两个部门的详细内容（包括用户数据）
     */
    private boolean compareDeptDetailContent(EmPrePlanDeptDTO currentDept, EmPrePlanDeptDTO historyDept) {
        try {
            // 比较部门基本信息
            if (!Objects.equals(currentDept.getDeptName(), historyDept.getDeptName()) ||
                !Objects.equals(currentDept.getDeptJob(), historyDept.getDeptJob()) ||
                !Objects.equals(currentDept.getEventLevel(), historyDept.getEventLevel()) ||
                !Objects.equals(currentDept.getDeptLevel(), historyDept.getDeptLevel()) ||
                !Objects.equals(currentDept.getOrderNum(), historyDept.getOrderNum())) {
                return false;
            }
            
            // 比较用户列表
            List<EmPrePlanDeptUserDTO> currentUsers = currentDept.getEmPrePlanDeptUserDTOList();
            List<EmPrePlanDeptUserDTO> historyUsers = historyDept.getEmPrePlanDeptUserDTOList();
            
            return compareDeptUsers(currentUsers, historyUsers);
            
        } catch (Exception e) {
            // 如果比较失败，认为不同
            return false;
        }
    }

    /**
     * 比较部门用户列表
     */
    private boolean compareDeptUsers(List<EmPrePlanDeptUserDTO> currentUsers, List<EmPrePlanDeptUserDTO> historyUsers) {
        // 处理null情况
        if (currentUsers == null && historyUsers == null) return true;
        if (currentUsers == null || historyUsers == null) return false;
        if (currentUsers.size() != historyUsers.size()) return false;
        
        // 按用户姓名和角色排序后比较
        List<EmPrePlanDeptUserDTO> sortedCurrent = new ArrayList<>(currentUsers);
        List<EmPrePlanDeptUserDTO> sortedHistory = new ArrayList<>(historyUsers);
        
        sortedCurrent.sort((a, b) -> {
            String keyA = (a.getLeaderName() != null ? a.getLeaderName() : "") + "|" + 
                         (a.getRoleName() != null ? a.getRoleName() : "");
            String keyB = (b.getLeaderName() != null ? b.getLeaderName() : "") + "|" + 
                         (b.getRoleName() != null ? b.getRoleName() : "");
            return keyA.compareTo(keyB);
        });
        
        sortedHistory.sort((a, b) -> {
            String keyA = (a.getLeaderName() != null ? a.getLeaderName() : "") + "|" + 
                         (a.getRoleName() != null ? a.getRoleName() : "");
            String keyB = (b.getLeaderName() != null ? b.getLeaderName() : "") + "|" + 
                         (b.getRoleName() != null ? b.getRoleName() : "");
            return keyA.compareTo(keyB);
        });
        
        // 逐个比较用户
        for (int i = 0; i < sortedCurrent.size(); i++) {
            if (!compareSingleUser(sortedCurrent.get(i), sortedHistory.get(i))) {
                return false;
            }
        }
        
        return true;
    }

    /**
     * 比较单个用户
     */
    private boolean compareSingleUser(EmPrePlanDeptUserDTO current, EmPrePlanDeptUserDTO history) {
        return Objects.equals(current.getLeaderName(), history.getLeaderName()) &&
               Objects.equals(current.getRoleName(), history.getRoleName()) &&
               Objects.equals(current.getPost(), history.getPost()) &&
               Objects.equals(current.getContact(), history.getContact()) &&
               Objects.equals(current.getDepId(), history.getDepId());
    }

    /**
     * 部门与路径的包装类
     */
    private static class DeptWithPath {
        private EmPrePlanDeptDTO dept;
        private String fullPath;
        
        public DeptWithPath(EmPrePlanDeptDTO dept, String fullPath) {
            this.dept = dept;
            this.fullPath = fullPath;
        }
        
        public EmPrePlanDeptDTO getDept() {
            return dept;
        }
        
        public String getFullPath() {
            return fullPath;
        }
    }

    /**
     * 比较处置措施
     */
    private List<EmPrePlanCompareVO.AssociatedDataDifference> compareMeasures(
            List<EmMeasureDTO> currentList, List<EmMeasureDTO> historyList) {
        return compareAssociatedData("处置措施", currentList, historyList,
                measure -> measure.getId() != null ? String.valueOf(measure.getId()) : "", 
                measure -> {
                    // 基于措施内容的前50个字符生成唯一标识符
                    String content = measure.getMeasureContent() != null ? measure.getMeasureContent().trim() : "";
                    String eventLevel = measure.getEventLevel() != null ? String.valueOf(measure.getEventLevel()) : "";
                    // 使用内容前50个字符 + 事件级别作为标识符
                    String contentPrefix = content.length() > 50 ? content.substring(0, 50) : content;
                    return eventLevel + "||" + contentPrefix;
                });
    }

    /**
     * 比较附件
     */
    private List<EmPrePlanCompareVO.AssociatedDataDifference> compareFiles(
            List<EmPrePlanFileDTO> currentList, List<EmPrePlanFileDTO> historyList) {
        return compareAssociatedData("附件", currentList, historyList,
                file -> file.getId() != null ? String.valueOf(file.getId()) : "", 
                file -> {
                    // 基于文件名和文件大小生成唯一标识符
                    String fileName = file.getFileName() != null ? file.getFileName().trim() : "";
                    String fileSize = file.getFileSize() != null ? String.valueOf(file.getFileSize()) : "0";
                    return fileName + "||" + fileSize;
                });
    }

    /**
     * 通用的关联数据比较方法
     * 修复：由于关联数据没有历史版本ID保存机制，改为基于内容进行比较
     */
    private <T> List<EmPrePlanCompareVO.AssociatedDataDifference> compareAssociatedData(
            String dataType, List<T> currentList, List<T> historyList,
            java.util.function.Function<T, String> idExtractor,
            java.util.function.Function<T, String> identifierExtractor) {
        
        List<EmPrePlanCompareVO.AssociatedDataDifference> differences = new ArrayList<>();
        
        // 由于关联数据没有历史版本ID保存机制，我们基于内容标识符进行比较
        Map<String, T> currentContentMap = new HashMap<>();
        Map<String, T> historyContentMap = new HashMap<>();
        
        // 构建当前版本数据映射（基于内容标识符）
        if (currentList != null) {
            for (T item : currentList) {
                String contentIdentifier = identifierExtractor.apply(item);
                if (contentIdentifier != null && !contentIdentifier.trim().isEmpty()) {
                    currentContentMap.put(contentIdentifier, item);
                }
            }
        }
        
        // 构建历史版本数据映射（基于内容标识符）
        if (historyList != null) {
            for (T item : historyList) {
                String contentIdentifier = identifierExtractor.apply(item);
                if (contentIdentifier != null && !contentIdentifier.trim().isEmpty()) {
                    historyContentMap.put(contentIdentifier, item);
                }
            }
        }
        
        // 找出所有唯一内容标识符
        Set<String> allContentIdentifiers = new HashSet<>();
        allContentIdentifiers.addAll(currentContentMap.keySet());
        allContentIdentifiers.addAll(historyContentMap.keySet());
        
        // 比较每个内容标识符对应的数据
        for (String contentIdentifier : allContentIdentifiers) {
            T currentItem = currentContentMap.get(contentIdentifier);
            T historyItem = historyContentMap.get(contentIdentifier);
            
            EmPrePlanCompareVO.AssociatedDataDifference diff = new EmPrePlanCompareVO.AssociatedDataDifference();
            diff.setDataType(dataType);
            diff.setDataId(contentIdentifier); // 使用内容标识符作为数据ID
            diff.setDataIdentifier(contentIdentifier);
            
            if (currentItem != null && historyItem != null) {
                // 两个版本都存在相同内容标识符的数据，比较详细内容
                boolean isContentSame = compareItemContent(currentItem, historyItem);
                if (!isContentSame) {
                    diff.setOperationType("MODIFIED");
                    diff.setCurrentData(currentItem);
                    diff.setHistoryData(historyItem);
                    differences.add(diff);
                }
                // 如果内容相同，则不添加到差异列表中
            } else if (currentItem != null) {
                // 只在当前版本存在，表示新增
                diff.setOperationType("ADDED");
                diff.setCurrentData(currentItem);
                diff.setHistoryData(null);
                differences.add(diff);
            } else {
                // 只在历史版本存在，表示删除
                diff.setOperationType("DELETED");
                diff.setCurrentData(null);
                diff.setHistoryData(historyItem);
                differences.add(diff);
            }
        }
        
        return differences;
    }

    /**
     * 比较两个对象的内容是否相同
     * 通过JSON序列化进行深度比较
     */
    private <T> boolean compareItemContent(T currentItem, T historyItem) {
        try {
            // 使用JSON序列化进行深度比较
            com.fasterxml.jackson.databind.ObjectMapper mapper = new com.fasterxml.jackson.databind.ObjectMapper();
            String currentJson = mapper.writeValueAsString(currentItem);
            String historyJson = mapper.writeValueAsString(historyItem);
            return Objects.equals(currentJson, historyJson);
        } catch (Exception e) {
            // 如果JSON序列化失败，使用toString比较
            return Objects.equals(currentItem.toString(), historyItem.toString());
        }
    }

    /**
     * 判断两个事件级别字符串是否有交集
     * 支持单个级别或多个级别（逗号分隔）的比较
     * 
     * @param eventLevel1 第一个事件级别字符串，如 "1" 或 "1,2,3"
     * @param eventLevel2 第二个事件级别字符串，如 "2" 或 "2,3,4"
     * @return 如果两个级别字符串有交集则返回true，否则返回false
     */
    private boolean hasEventLevelIntersection(String eventLevel1, String eventLevel2) {
        // 空值检查
        if (eventLevel1 == null || eventLevel2 == null || 
            eventLevel1.trim().isEmpty() || eventLevel2.trim().isEmpty()) {
            return false;
        }
        
        // 将级别字符串按逗号分割并去除空格
        String[] levels1 = eventLevel1.split(",");
        String[] levels2 = eventLevel2.split(",");
        
        // 将第一个级别数组转换为Set，便于快速查找
        Set<String> levelSet1 = new HashSet<>();
        for (String level : levels1) {
            String trimmedLevel = level.trim();
            if (!trimmedLevel.isEmpty()) {
                levelSet1.add(trimmedLevel);
            }
        }
        
        // 检查第二个级别数组中是否有任何元素存在于第一个级别集合中
        for (String level : levels2) {
            String trimmedLevel = level.trim();
            if (!trimmedLevel.isEmpty() && levelSet1.contains(trimmedLevel)) {
                return true; // 找到交集，立即返回true
            }
        }
        
        return false; // 没有找到交集
    }
}
