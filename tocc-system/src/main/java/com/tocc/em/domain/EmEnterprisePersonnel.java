package com.tocc.em.domain;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.tocc.common.annotation.Excel;
import com.tocc.common.core.domain.BaseEntity;

/**
 * 企业人员信息对象 em_enterprise_personnel
 * 
 * <AUTHOR>
 * @date 2025-06-18
 */
 @Data
public class EmEnterprisePersonnel extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    @ApiModelProperty(value = "主键")
    private String enterprisePersonnelId;

    /** 企业名称 */
    @Excel(name = "企业名称")
    @ApiModelProperty(value ="企业名称")
    private String enterpriseName;

    /** 负责人姓名 */
    @Excel(name = "负责人姓名")
    @ApiModelProperty(value = "负责人姓名")
    private String principal;

    /** 联系电话 */
    @Excel(name = "联系电话")
    @ApiModelProperty(value = "联系电话")
    private String contactWay;

    /** 创建人 */
    @Excel(name = "创建人")
    @ApiModelProperty(value = "创建人")
    private String creator;

    /** 更新人 */
    @Excel(name = "更新人")
    @ApiModelProperty(value = "更新人")
    private String updater;

    /** 删除标志(0存在/1删除) */
    @ApiModelProperty(value = "删除标志(0存在/1删除)")
    private Integer delFlag;


}
