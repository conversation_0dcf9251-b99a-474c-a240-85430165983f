package com.tocc.em.mapper;

import java.util.List;
import com.tocc.em.domain.EmEnterprisePersonnel;

/**
 * 企业人员信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-18
 */
public interface EmEnterprisePersonnelMapper 
{
    /**
     * 查询企业人员信息
     * 
     * @param enterprisePersonnelId 企业人员信息主键
     * @return 企业人员信息
     */
    public EmEnterprisePersonnel selectEmEnterprisePersonnelByEnterprisePersonnelId(String enterprisePersonnelId);

    /**
     * 查询企业人员信息列表
     * 
     * @param emEnterprisePersonnel 企业人员信息
     * @return 企业人员信息集合
     */
    public List<EmEnterprisePersonnel> selectEmEnterprisePersonnelList(EmEnterprisePersonnel emEnterprisePersonnel);

    /**
     * 新增企业人员信息
     * 
     * @param emEnterprisePersonnel 企业人员信息
     * @return 结果
     */
    public int insertEmEnterprisePersonnel(EmEnterprisePersonnel emEnterprisePersonnel);

    /**
     * 修改企业人员信息
     * 
     * @param emEnterprisePersonnel 企业人员信息
     * @return 结果
     */
    public int updateEmEnterprisePersonnel(EmEnterprisePersonnel emEnterprisePersonnel);

    /**
     * 删除企业人员信息
     * 
     * @param enterprisePersonnelId 企业人员信息主键
     * @return 结果
     */
    public int deleteEmEnterprisePersonnelByEnterprisePersonnelId(String enterprisePersonnelId);

    /**
     * 批量删除企业人员信息
     * 
     * @param enterprisePersonnelIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteEmEnterprisePersonnelByEnterprisePersonnelIds(String[] enterprisePersonnelIds);
}
