package com.tocc.em.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.tocc.common.annotation.Excel;
import com.tocc.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.List;

/**
 * 事件分级响应条件DTO
 *
 * <AUTHOR>
 * @date 2025-05-31
 */
@Data
public class EmEventLevelDTO extends BaseEntity
{

    /** 主键 */
    @ApiModelProperty(value = "主键")
    private String id;

    /** 关联的预案ID */
    @Excel(name = "关联的预案ID")
    @ApiModelProperty(value = "关联的预案ID")
    private String prePlanId;

    /** 版本号 */
    @Excel(name = "版本号")
    @ApiModelProperty(value = "版本号")
    private String version;

    /** 事件级别 */
    @Excel(name = "事件级别")
    @ApiModelProperty(value = "事件级别")
    private Integer eventLevel;

    /** 事件响应内容 */
    @Excel(name = "事件响应条件")
    @ApiModelProperty(value = "事件响应条件")
    private String conditions;

    /** 流程图 */
    @Excel(name = "流程图")
    @ApiModelProperty(value = "流程图")
    private String processFlow;

    /** 创建人 */
    @Excel(name = "创建人")
    @ApiModelProperty(value = "创建人")
    private String creator;

    /** 更新人 */
    @Excel(name = "更新人")
    @ApiModelProperty(value = "更新人")
    private String updater;

    /** 删除标志(0存在 1删除) */
    @Excel(name = "删除标志(0存在 1删除)")
    @ApiModelProperty(value = "删除标志(0存在 1删除)")
    private Integer delFlag;

    /**
     * 响应单位
     */
    @ApiModelProperty(value = "响应单位")
    @JsonProperty("emPrePlanDeptDTOList")
    List<EmPrePlanDeptDTO> emPrePlanDeptDTOList;
}
